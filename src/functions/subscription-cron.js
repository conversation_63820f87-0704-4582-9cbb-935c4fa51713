// CRITICAL: Initialize Key Vault secrets before Azure Functions runtime starts
require('../config/early-init')

const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')

// Timer trigger to process expired subscriptions
// Runs every day at 00:00 (midnight)
app.timer('subscription-expired-cron', {
  schedule: '0 0 0 * * *', // Midnight every day
  handler: async (myTimer, context) => {
    context.log(
      'Subscription expired cron job triggered at:',
      new Date().toISOString(),
    )

    try {
      // Create a mock request object for the handler
      const mockReq = {
        query: {
          get: (key) => null,
        },
      }

      // Create a mock decode object (system operation)
      const mockDecode = {
        oid: 'system',
        name: 'System Cron Job',
      }

      const result = await subscriptionHandler.processExpiredSubscriptions(
        mockReq,
        mockDecode,
      )

      context.log('Subscription expired cron job completed:', result)
    } catch (error) {
      context.error('Error in subscription-expired-cron:', error)
    }
  },
})

// Timer trigger to notify about upcoming renewals
// Runs every day at 09:00 AM
app.timer('subscription-renewal-reminder', {
  schedule: '0 0 9 * * *', // 9 AM every day
  handler: async (myTimer, context) => {
    context.log(
      'Subscription renewal reminder triggered at:',
      new Date().toISOString(),
    )

    try {
      const subscriptionService = require('../services/subscription-service')

      // Get subscriptions expiring in the next 7 days
      const result = await subscriptionService.getUpcomingRenewals(7)

      if (result.success && result.data.length > 0) {
        context.log(
          `Found ${result.count} subscriptions expiring in the next 7 days`,
        )

        // Here you can integrate with email service to send renewal reminders
        // const emailService = require('../services/email-service')
        // for (const subscription of result.data) {
        //   await emailService.sendRenewalReminder(subscription)
        // }

        context.log('Renewal reminders processed successfully')
      } else {
        context.log('No upcoming renewals found')
      }
    } catch (error) {
      context.error('Error in subscription-renewal-reminder:', error)
    }
  },
})
