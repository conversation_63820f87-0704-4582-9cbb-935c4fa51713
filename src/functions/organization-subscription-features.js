const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { validateToken } = require('../common/user-validation')

app.http('organization-subscription-features', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'organization/subscription/features',
  handler: async (request, context) => {
    try {
      // Validate authentication token
      const authResult = await validateToken(request)
      if (authResult.message !== 'SUCCESS') {
        return {
          status: 401,
          jsonBody: {
            success: false,
            message: 'Unauthorized access',
          },
        }
      }

      // Call the subscription handler
      const result = await subscriptionHandler.getOrganizationSubscriptionFeatures(
        request,
        authResult.decode,
      )

      return result
    } catch (error) {
      context.log.error('Error in organization-subscription-features function:', error)
      return {
        status: 500,
        jsonBody: {
          success: false,
          message: 'Internal server error',
        },
      }
    }
  },
})
