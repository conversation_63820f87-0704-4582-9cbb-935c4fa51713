const { app } = require('@azure/functions')
const invoiceHandler = require('../handlers/invoice-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Invoice Management (Payment Invoices)
app.http('invoice', {
  methods: ['GET'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.get:
          const invoiceId = req.query.get('id')

          if (invoiceId) {
            return await invoiceHandler.getInvoiceById(req, decode)
          } else {
            return await invoiceHandler.getAllInvoices(req, decode)
          }

        default:
          return jsonResponse(
            'Method not allowed. Only GET is supported for invoice.',
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (error) {
      context.error('Error in invoice function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
