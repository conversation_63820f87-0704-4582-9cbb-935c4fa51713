const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')
const { validateToken } = require('../common/user-validation')

// Get features under a subscription
app.http('subscription-features', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'subscription/features',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)

    try {
      if (req.method === HttpMethod.get) {
        // Validate token
        const decode = await validateToken(req)
        if (!decode) {
          return jsonResponse(
            'Unauthorized access',
            HttpStatusCode.Unauthorized,
          )
        }

        return await subscriptionHandler.getSubscriptionFeatures(req, decode)
      }

      return jsonResponse(
        'Method not allowed. Only GET is supported.',
        HttpStatusCode.MethodNotAllowed,
      )
    } catch (error) {
      context.error('Error in subscription-features function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
