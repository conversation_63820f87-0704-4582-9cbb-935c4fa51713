const { ORGANIZATION_PLAN_ID } = require('../models/subscription-model')

module.exports = {
  // Subscription Plan Queries
  getActivePlansQuery: () => {
    return `SELECT * FROM c WHERE c.isActive = true AND c.id != '${ORGANIZATION_PLAN_ID}' ORDER BY c.created_on DESC`
  },

  searchPlansQuery: (searchParams) => {
    const conditions = ['c.isActive = true', `c.id != '${ORGANIZATION_PLAN_ID}'`]
    const parameters = []

    if (searchParams.planName) {
      conditions.push('CONTAINS(LOWER(c.planName), LOWER(@planName))')
      parameters.push({ name: '@planName', value: searchParams.planName })
    }

    if (searchParams.validity) {
      conditions.push('c.validity = @validity')
      parameters.push({ name: '@validity', value: searchParams.validity })
    }

    return {
      query: `SELECT * FROM c WHERE ${conditions.join(' AND ')} ORDER BY c.created_on DESC`,
      parameters: parameters,
    }
  },

  getPlansByValidityQuery: (validity) => {
    return `SELECT * FROM c WHERE c.isActive = true AND c.id != '${ORGANIZATION_PLAN_ID}' AND c.validity = '${validity}' ORDER BY c.created_on DESC`
  },

  getPlanFeaturesQuery: (planId) => {
    return `SELECT c.id, c.planName, c.features, c.addOnFeatures FROM c WHERE c.id = '${planId}'`
  },

  // Organization Subscription Queries
  // Returns active or pending subscriptions (pending includes free trials without payment)
  getActiveSubscriptionByOrganizationQuery: (organizationId) => {
    return `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status IN ('active', 'pending') ORDER BY c.created_on DESC`
  },

  getSubscriptionsByStatusQuery: (status, organizationId = null) => {
    let query = `SELECT * FROM c WHERE c.status = '${status}'`
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`
    }
    query += ' ORDER BY c.created_on DESC'
    return query
  },

  getSubscriptionAnalyticsQuery: (organizationId = null) => {
    const baseQuery = organizationId ? `WHERE c.organizationId = '${organizationId}'` : ''
    return `
      SELECT
        COUNT(1) as totalSubscriptions,
        SUM(CASE WHEN c.status = 'active' THEN 1 ELSE 0 END) as activeSubscriptions,
        SUM(CASE WHEN c.status = 'expired' THEN 1 ELSE 0 END) as expiredSubscriptions,
        SUM(CASE WHEN c.status = 'cancelled' THEN 1 ELSE 0 END) as cancelledSubscriptions,
        SUM(c.totalAmount) as totalRevenue
      FROM c
      ${baseQuery}
    `
  },

  getUpcomingRenewalsQuery: (daysAhead, organizationId = null) => {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + daysAhead)

    let query = `
      SELECT * FROM c
      WHERE c.status = 'active'
      AND c.autoRenew = true
      AND c.endDate <= '${futureDate.toISOString()}'
      AND c.endDate >= '${new Date().toISOString()}'
    `

    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`
    }

    query += ' ORDER BY c.endDate ASC'
    return query
  },

  getSubscriptionsByDateRangeQuery: (startDate, endDate, organizationId = null) => {
    let query = `SELECT * FROM c WHERE c.created_on >= '${startDate}' AND c.created_on <= '${endDate}'`
    if (organizationId) {
      query += ` AND c.organizationId = '${organizationId}'`
    }
    query += ' ORDER BY c.created_on DESC'
    return query
  },

  getRevenueByPlanQuery: (startDate = null, endDate = null) => {
    let dateFilter = ''
    if (startDate && endDate) {
      dateFilter = `AND c.created_on >= '${startDate}' AND c.created_on <= '${endDate}'`
    }

    return `
      SELECT c.planName, c.planId,
             COUNT(1) as subscriptionCount,
             SUM(c.totalAmount) as totalRevenue
      FROM c
      WHERE c.status IN ('active', 'expired')
      ${dateFilter}
      GROUP BY c.planName, c.planId
      ORDER BY totalRevenue DESC
    `
  },

  getActiveSubscriptionFeatureAccessQuery: (organizationId) => {
    return `SELECT c.id, c.planName, c.features, c.addOnFeatures FROM c WHERE c.organizationId = '${organizationId}' AND c.status IN ('active', 'pending')`
  },

  // Additional subscription queries
  getAllPlansQuery: () => {
    return `SELECT * FROM c WHERE c.isActive = true AND c.id != '${ORGANIZATION_PLAN_ID}' ORDER BY c.created_on DESC`
  },

  getOrganizationSubscriptionQuery: (organizationId) => {
    return `
      SELECT * FROM c
      WHERE c.organizationId = '${organizationId}'
      AND c.status = 'active'
      ORDER BY c.created_on DESC
    `
  },

  getOrganizationSubscriptionHistoryQuery: (organizationId) => {
    return `
      SELECT * FROM c
      WHERE c.organizationId = '${organizationId}'
      ORDER BY c.created_on DESC
    `
  },

  getSubscriptionByIdQuery: (subscriptionId) => {
    return `SELECT * FROM c WHERE c.id = '${subscriptionId}'`
  },

  getExpiringSubscriptionsQuery: (daysBeforeExpiry = 7) => {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + daysBeforeExpiry)

    return `
      SELECT * FROM c
      WHERE c.status = 'active'
      AND c.endDate <= '${futureDate.toISOString()}'
      AND c.endDate >= '${new Date().toISOString()}'
      ORDER BY c.endDate ASC
    `
  },

  getExpiredSubscriptionsQuery: () => {
    return `
      SELECT * FROM c
      WHERE (c.status = 'active' OR c.status = 'pending')
      AND c.endDate < '${new Date().toISOString()}'
    `
  },

  getSubscriptionsByPlanIdQuery: (planId) => {
    return `SELECT * FROM c WHERE c.planId = "${planId}"`
  },

  hasUsedFreeTrialQuery: (contactEmail) => {
    return `SELECT * FROM c WHERE c.contactEmail = "${contactEmail}" AND c.trialUsed = true`
  },

  getActiveSubscriptionByEmailAndOrganizationQuery: (email, organizationId) => {
    return {
      query: `SELECT * FROM c WHERE c.organizationId = @organizationId
              AND c.contactEmail = @email
              AND (c.status = 'active' OR c.status = 'pending')
              ORDER BY c.created_on DESC`,
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@email', value: email }
      ]
    }
  },

  getAnySubscriptionByEmailAndOrganizationQuery: (email, organizationId) => {
    return {
      query: `SELECT * FROM c WHERE c.organizationId = @organizationId
              AND c.contactEmail = @email
              ORDER BY c.created_on DESC`,
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@email', value: email }
      ]
    }
  },
}
