const getAllOrganizationsQuery = (nameFilter) => {
  let query = 'SELECT * FROM c'
  if (nameFilter) {
    query += ` WHERE CONTAINS(LOWER(c.name), LOWER("${nameFilter}"))`
  }
  query += ' ORDER BY c._ts DESC'
  return query
}

const getOrganizationByIdQuery = (id) => {
  return `SELECT * FROM c WHERE c.id = "${id}"`
}

const getOrganizationByNameQuery = (name) => {
  return `SELECT * FROM c WHERE c.name = "${name}"`
}

const getOrganizationByEmailQuery = (email) => {
  return `SELECT * FROM c WHERE c.contactEmail = "${email}"`
}

module.exports = {
  getAllOrganizationsQuery,
  getOrganizationByIdQuery,
  getOrganizationByNameQuery,
  getOrganizationByEmailQuery,
}
