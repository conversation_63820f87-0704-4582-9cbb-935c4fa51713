// const msal = require('@azure/msal-node');
const logging = require('./logging')
const userService = require('../services/user-service')
const jwt = require('jsonwebtoken')
const jwksClient = require('jwks-rsa')
const axios = require('axios').default
const rolePermissionHandler = require('../handlers/role-permission-handler')
// const RedisCacheHandler = require('../services/redis-service')
const https = require('https')
const { AuthMessage } = require('./constant')
const agent = new https.Agent({
  rejectUnauthorized: false,
})
const exp = require('constants')
const fs = require('fs')
const path = require('path')
const { DefaultRoles } = require('./roles')
const { permission } = require('process')

const { COMMON_APIS } = require('../utils/common-apis')

const secretManager = require('../services/secret-manager')

// Initialize tenant configuration
let TENANT_NAME = null
let SIGNIN_POLICY = null
let isInitialized = false

async function initializeTenantConfig() {
  if (!isInitialized) {
    TENANT_NAME = await secretManager.getSecret('TENANT_NAME')
    SIGNIN_POLICY = await secretManager.getSecret('signin_policy')
    isInitialized = true
  }
}
const EXPIRED_TIME = 10 * 60 * 1000

const getSigningKeys = async () => {
  await initializeTenantConfig()
  const jwksUri = `https://${TENANT_NAME}.b2clogin.com/${TENANT_NAME}.onmicrosoft.com/discovery/v2.0/keys?p=${SIGNIN_POLICY}`
  const response = await axios.get(jwksUri, {
    httpsAgent: agent,
  })
  return response.data.keys
}

let client = null
async function getJwksClient() {
  if (!client) {
    await initializeTenantConfig()
    client = jwksClient({
      jwksUri: `https://${TENANT_NAME}.b2clogin.com/${TENANT_NAME}.onmicrosoft.com/discovery/v2.0/keys?p=${SIGNIN_POLICY}`,
    })
  }
  return client
}
async function getKey(header, callback) {
  try {
    const jwksClientInstance = await getJwksClient()
    jwksClientInstance.getSigningKey(header.kid, function (err, key) {
      if (err) {
        callback(err, null)
        return
      }
      const signingKey = key.getPublicKey()
      callback(null, signingKey)
    })
  } catch (error) {
    callback(error, null)
  }
}
const verifyToken = async (token) => {
  try {
    const keys = await getSigningKeys()
    const decodedToken = jwt.decode(token, { complete: true })

    const kid = decodedToken.header.kid
    const signingKey = keys.find((key) => key.kid === kid)

    if (!signingKey) {
      throw new Error('Signing key not found')
    }

    return new Promise((resolve, reject) => {
      jwt.verify(token, getKey, { algorithms: ['RS256'] }, (err, decoded) => {
        if (err) {
          return reject(err)
        }
        resolve(decoded)
      })
    })
  } catch (error) {
    // Fallback: Verify using RSA public key
    const publicKey = fs.readFileSync(
      path.resolve(__dirname, '../keys/public.pem'),
      'utf8',
    )

    return new Promise((resolve, reject) => {
      jwt.verify(
        token,
        publicKey,
        { algorithms: ['RS256'] },
        (err, decoded) => {
          if (err) {
            return reject(err)
          }
          resolve(decoded)
        },
      )
    })
  }
}
class ValidateUser {
  /**
   * @param {*} req azure functin req
   * @param {*} context azure functin context
   * @param {*} idToken jwt token frm request header
   * @param {*} role user role need to verify
   * @returns
   */
  async doValidate(req) {
    try {
      const environment = await secretManager.getSecret('environment')
      if (environment && environment == 'local') {
        return {
          message: AuthMessage.SUCCESS,
          decode: {
            oid: 'local_debugging',
          },
        }
      }
      var url = req.url
      var pathname = new URL(url).pathname

      // Remove query parameters if present
      pathname = pathname?.split('?')[0]

      var apiName = null

      // Handle different URL patterns:
      // Pattern 1: /EMR-MS/api/v0.1/book-consultation/future
      // Pattern 2: /consultation/v0.1/book-consultation/future
      // Pattern 3: /api/book-consultation/future (local)

      if (pathname.includes('/api/')) {
        // Pattern 1 & 3: Extract everything after '/api/'
        apiName = pathname.split('/api/')[1]
      } else {
        // Pattern 2: Extract everything after version pattern or use full path
        const versionMatch = pathname.match(/\/v\d+\.\d+\/(.+)/)
        if (versionMatch) {
          apiName = versionMatch[1]
        } else {
          // Fallback: remove leading slash and use the path
          apiName = pathname.startsWith('/') ? pathname.substring(1) : pathname
        }
      }

      if (!apiName) {
        return {
          message: AuthMessage.SUCCESS,
          decode: {
            oid: 'local_debugging',
          },
        }
      }

      // Handle API name parsing for routes with forward slashes
      // Since we've already extracted the API name, now we just need to check
      // if it should preserve slashes or convert them to dashes
      if (apiName && apiName.includes('/')) {
        const routesWithSlashes = [
          'lab-test/departments',
          'lab-test-departments',
          'book-consultation/future',
          'book-consultation/type',
          'book-consultation/queue',
          'patient/consulting',
          'patient/history',
          'patient/vitals',
          'patient/lifestyle',
          'patient/diagnosis-notes',
          'patient/lifestyle/medical-history-addiction',
          'permissions/api-list',
          'loinc/list',
          'loinc/update',
          'loinc/remove',
          'loinc/tests-for-organization',
          'organization/patients',
          'organization/medicines',
          'organization/medicines/update',
          'organization/medicines/remove',
          'appointment/queue',
          'patient-lab-test/details',
          'patient-lab-test/search',
          'lab-tests/search',
          'package/tests',
          'packages/user-specific',
          'prescription-package/medicines',
          'prescription-package/details',
          'prescriptions/details',
          'prescriptions/search',
          'payments/create-order',
          'payments/verify',
          'payments/webhook',
          'payments/details',
          'payments/organization',
          'payments/stats',
          'payments/search',
          'physical-activity/dashboard',
          'food/list',
          'food/serving-unit',
          'doctor/profile-picture/upload',
          'doctor/profile-picture/url',
          'doctor/customise-emr',
          'doctor/summary',
          'user/document/upload',
          'user/list',
          'user/set-password',
          'auth/login',
          'auth/reset-password',
          'lifestyle/question',
          'lifestyle/ambient-listening',
          'lifestyle/summary',
        ]

        const matchingRoute = routesWithSlashes.find((route) =>
          apiName.includes(route),
        )
        if (matchingRoute) {
          apiName = matchingRoute
        } else {
          apiName = apiName.split('/').join('-')
        }
      }

      var method = req.method
      if (apiName === 'payments-webhook') {
        return {
          message: AuthMessage.SUCCESS,
          decode: {
            oid: 'razorpay_webhook',
          },
        }
      }
      // Exclude token validation for specific endpoints
      if (apiName === 'auth-login' || apiName === 'user-set-password') {
        return {
          message: AuthMessage.SUCCESS,
          decode: null,
        }
      }
      if (apiName == 'usersignup') {
        return {
          message: AuthMessage.SUCCESS,
          decode: {
            oid: 'Azure B2C',
          },
        }
      }
      const token = req.headers.get('authorization')
      var idToken = token?.split(' ')[1] || null
      if (!idToken) {
        return {
          message: AuthMessage.MISSING_TOKEN,
          decode: null,
        }
      }
      var decode = await verifyToken(idToken)
      if (decode) {
        var userid = decode.oid
        //--------------- Commented Code
        // let lastLogin = await RedisCacheHandler.get(userid)
        // if (!lastLogin) {
        //   await RedisCacheHandler.set(userid, new Date().toISOString())
        // } else {
        //   const now = new Date()
        //   const last = new Date(lastLogin)
        //   const diff = now - last

        //   if (diff >= EXPIRED_TIME) {
        //     await RedisCacheHandler.delete(userid)
        //     return {
        //       message: AuthMessage.SESSION_EXPIRED,
        //       decode: null,
        //     }
        //   }

        //   await RedisCacheHandler.set(userid, new Date().toISOString())
        //   lastLogin = await RedisCacheHandler.get(userid)
        // }
        //---------------
        var isValidUser = await checkUserRole(apiName, method, userid)
        if (!isValidUser) {
          return {
            message: AuthMessage.NO_PERMISSING,
            decode: null,
          }
        }
        return {
          message: AuthMessage.SUCCESS,
          decode: decode,
        }
      }
      return {
        message: AuthMessage.COMMON_AUTH_FAILED,
        decode: null,
      }
    } catch (error) {
      logging.logError('', error)
      return {
        message: error.message,
        decode: null,
      }
    }
  }
}

async function checkUserRole(apiName, method, userid) {
  try {
    let user = await userService.getUserByB2CUserId(userid)
    if (!user) {
      user = await userService.getUserById(userid)
    }

    if (!user) {
      logging.logInfo(`User not found: ${userid}`)
      return false
    }

    const userRole = user?.userRole || ''
    const roleId = user?.roleId || ''

    logging.logInfo(
      `Checking user permissions for ${userid}: userRole=${userRole}, roleId=${roleId}, apiName=${apiName}, method=${method}`,
    )

    if (userRole === DefaultRoles.SUPER_ADMIN) {
      logging.logInfo(
        `User ${userid} granted unrestricted access as Super Admin`,
      )
      return true
    }

    // Check if user is clinic type by checking if they have user-sys-role entry
    const { UserType } = require('./constant')
    const userSysRoleRepository = require('../repositories/user-sys-role-repository')
    const userSysRole = await userSysRoleRepository.getUserSysRoleByUserId(
      user.id,
    )

    if (userSysRole && userSysRole.isActive) {
      // User is clinic type - check permissions from user-sys-role
      logging.logInfo(
        `User ${userid} is clinic type, checking permissions from user-sys-role`,
      )

      // Get permission keys from user-sys-role and check if any match the API
      const permissionKeys = userSysRole.permissionKeys || []
      const { APIPermissions } = require('./permissions')

      // Find matching permission for this API and method
      const matchingPermission = APIPermissions.find(
        (permission) =>
          permission.apis.includes(apiName) &&
          permission.methods.includes(method.toUpperCase()),
      )

      if (
        matchingPermission &&
        permissionKeys.includes(matchingPermission.key)
      ) {
        logging.logInfo(
          `Clinic user ${userid} has permission ${matchingPermission.key} for ${apiName} ${method}`,
        )
        return true
      }

      // Check if this is a common API that doesn't require specific permissions
      const isCommonAPI = COMMON_APIS.some(
        (commonApi) => apiName === commonApi || apiName.includes(commonApi),
      )

      if (isCommonAPI) {
        logging.logInfo(
          `Allowing access to common API ${apiName} for clinic user ${userid}`,
        )
        return true
      }

      logging.logInfo(
        `Clinic user ${userid} does not have permission for ${apiName} ${method}`,
      )
      return false
    }

    if (
      roleId &&
      userRole !== DefaultRoles.ORGANIZATION_ADMIN &&
      userRole !== DefaultRoles.DOCTOR &&
      userRole !== DefaultRoles.NURSE &&
      userRole !== DefaultRoles.RECEPTIONIST &&
      userRole !== DefaultRoles.ORGANIZATION_SUPER_ADMIN
    ) {
      const organizationId = user?.organizationId || ''
      let rolePermissions = null

      if (organizationId) {
        rolePermissions =
          await rolePermissionHandler.getAPIListbyRoleIdAndOrganization(
            roleId,
            organizationId,
          )
      }

      if (!rolePermissions) {
        rolePermissions = await rolePermissionHandler.getAPIListbyRoleId(roleId)
      }

      if (!rolePermissions || !rolePermissions.APIs) {
        logging.logInfo(
          `No role permissions found for user ${userid} with roleId ${roleId}`,
        )
        return false
      }
      // Find API permission that matches both API name and HTTP method
      const apiPermission = rolePermissions.APIs.find(
        (api) =>
          api.api === apiName && api.methods.includes(method.toUpperCase()),
      )

      if (!apiPermission) {
        // Check if this is a common API that doesn't require specific permissions
        const isCommonAPI = COMMON_APIS.some(
          (commonApi) => apiName === commonApi || apiName.includes(commonApi),
        )

        if (isCommonAPI) {
          logging.logInfo(
            `Allowing access to common API ${apiName} for user ${userid} without specific permission`,
          )
          return true
        }

        logging.logInfo(
          `No API permission found for ${apiName} with method ${method} for user ${userid} with roleId ${roleId}`,
        )
        return false
      }

      const hasMethodPermission = true // Already verified in find condition

      logging.logInfo(
        `Method permission check for ${method} on ${apiName}: ${hasMethodPermission} (roleId: ${roleId}, organizationId: ${organizationId})`,
      )
      return hasMethodPermission
    }
    logging.logInfo(
      `No roleId found for user ${userid}, falling back to role-based checking`,
    )

    if (
      userRole === DefaultRoles.ORGANIZATION_ADMIN ||
      userRole === DefaultRoles.DOCTOR ||
      userRole === DefaultRoles.NURSE ||
      userRole === DefaultRoles.RECEPTIONIST ||
      userRole === DefaultRoles.ORGANIZATION_SUPER_ADMIN
    ) {
      logging.logInfo(
        `User ${userid} granted access via default role: ${userRole}`,
      )
      return true
    }

    const organizationId = user?.organizationId || ''
    let rolePermissions = null

    if (organizationId) {
      rolePermissions =
        await rolePermissionHandler.getAPIListbyRoleAndOrganization(
          userRole,
          organizationId,
        )
    }

    // Fallback to general role permissions if no organization-specific permissions found
    // if (!rolePermissions || !rolePermissions.APIs) {
    //   rolePermissions = await rolePermissionHandler.getAPIListbyRole(userRole)
    // }

    if (!rolePermissions || !rolePermissions.APIs) {
      logging.logInfo(
        `No role permissions found for user ${userid} with role ${userRole}`,
      )
      return false
    }

    // Find API permission that matches both API name and HTTP method
    const apiPermission = rolePermissions.APIs.find(
      (api) =>
        api.api === apiName && api.methods.includes(method.toUpperCase()),
    )

    if (!apiPermission) {
      // Check if this is a common API that doesn't require specific permissions
      const isCommonAPI = COMMON_APIS.some(
        (commonApi) => apiName === commonApi || apiName.includes(commonApi),
      )

      if (isCommonAPI) {
        logging.logInfo(
          `Allowing access to common API ${apiName} for user ${userid} without specific permission (fallback role: ${userRole})`,
        )
        return true
      }

      logging.logInfo(
        `No API permission found for ${apiName} with method ${method} for user ${userid} with role ${userRole}`,
      )
      return false
    }

    const hasMethodPermission = true // Already verified in find condition

    logging.logInfo(
      `Method permission check for ${method} on ${apiName}: ${hasMethodPermission} (fallback role: ${userRole})`,
    )
    return hasMethodPermission
  } catch (error) {
    logging.logError(`Error in checkUserRole for ${userid}:`, error)
    return false
  }
}

module.exports = new ValidateUser()
