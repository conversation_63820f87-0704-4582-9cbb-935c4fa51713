const logging = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const UserSysRole = require('../models/user-sys-role-model')

class UserSysRoleRepository {
  constructor() {
    this.containerName = 'user_sys_role'
  }

  async createUserSysRole(userSysRoleData) {
    try {
      const userSysRole = new UserSysRole(userSysRoleData)

      const validation = userSysRole.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      // Generate permission keys from features
      userSysRole.generatePermissionKeys()

      const resource = await cosmosDbContext.createItem(userSysRole.toJSON(), this.containerName)
      return resource
    } catch (error) {
      logging.logError('Error creating user sys role:', error)
      throw error
    }
  }

  async updateUserSysRole(userSysRoleData) {
    try {
      const userSysRole = new UserSysRole(userSysRoleData)

      const validation = userSysRole.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      // Generate permission keys from features
      userSysRole.generatePermissionKeys()

      const resource = await cosmosDbContext.upsertItem(
        userSysRole.id,
        userSysRole.toJSON(),
        this.containerName
      )
      return resource
    } catch (error) {
      logging.logError('Error updating user sys role:', error)
      throw error
    }
  }

  async getUserSysRoleByUserId(userId) {
    try {
      const query = `SELECT * FROM c WHERE c.userId = '${userId}' AND c.isActive = true`
      const data = await cosmosDbContext.queryItems(query, this.containerName)
      return data && data.length > 0 ? data[0] : null
    } catch (error) {
      logging.logError(`Error getting user sys role by user ID: ${userId}`, error)
      return null
    }
  }

  async getUserSysRoleBySubscriberId(subscriberId) {
    try {
      const query = `SELECT * FROM c WHERE c.subscriberId = '${subscriberId}' AND c.isActive = true`
      const data = await cosmosDbContext.queryItems(query, this.containerName)
      return data || []
    } catch (error) {
      logging.logError(`Error getting user sys roles by subscriber ID: ${subscriberId}`, error)
      return []
    }
  }

  async getUserSysRoleBySubscriptionId(subscriptionId) {
    try {
      const query = `SELECT * FROM c WHERE c.subscriptionId = '${subscriptionId}' AND c.isActive = true`
      const data = await cosmosDbContext.queryItems(query, this.containerName)
      return data || []
    } catch (error) {
      logging.logError(`Error getting user sys roles by subscription ID: ${subscriptionId}`, error)
      return []
    }
  }

  async deleteUserSysRole(id) {
    try {
      const result = await cosmosDbContext.deleteItem(id, id, this.containerName)
      return result
    } catch (error) {
      logging.logError(`Error deleting user sys role: ${id}`, error)
      throw error
    }
  }

  async deactivateUserSysRole(id) {
    try {
      const existingRole = await cosmosDbContext.readItem(id, id, this.containerName)
      if (!existingRole) {
        throw new Error('User sys role not found')
      }

      existingRole.isActive = false
      existingRole.updated_on = new Date().toISOString()

      const result = await cosmosDbContext.upsertItem(id, existingRole, this.containerName)
      return result
    } catch (error) {
      logging.logError(`Error deactivating user sys role: ${id}`, error)
      throw error
    }
  }

  async getUserSysRolesByOrganization(organizationId) {
    try {
      const query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.isActive = true`
      const data = await cosmosDbContext.queryItems(query, this.containerName)
      return data || []
    } catch (error) {
      logging.logError(`Error getting user sys roles by organization: ${organizationId}`, error)
      return []
    }
  }
}

module.exports = new UserSysRoleRepository()
