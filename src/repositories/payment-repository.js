const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const paymentQuery = require('../queries/payment-query')

const paymentsContainer = 'Payments'

class PaymentRepository {
  async createPayment(payment) {
    try {
      return await cosmosDbContext.createItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error creating payment record', error)
      throw new Error(`Failed to create payment: ${error.message}`)
    }
  }

  async updatePayment(payment) {
    try {
      return await cosmosDbContext.updateItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error updating payment record', error)
      throw new Error(`Failed to update payment: ${error.message}`)
    }
  }

  async getPaymentById(paymentId) {
    try {
      const query = paymentQuery.getPaymentByIdQuery(paymentId)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by ID', error)
      throw new Error(`Failed to fetch payment by ID: ${error.message}`)
    }
  }

  async getPaymentByRazorpayOrderId(razorpayOrderId) {
    try {
      const query =
        paymentQuery.getPaymentByRazorpayOrderIdQuery(razorpayOrderId)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by Razorpay order ID', error)
      throw new Error(
        `Failed to fetch payment by Razorpay order ID: ${error.message}`,
      )
    }
  }

  async getPaymentByRazorpayPaymentId(razorpayPaymentId) {
    try {
      const query =
        paymentQuery.getPaymentByRazorpayPaymentIdQuery(razorpayPaymentId)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by Razorpay payment ID', error)
      throw new Error(
        `Failed to fetch payment by Razorpay payment ID: ${error.message}`,
      )
    }
  }

  async getPaymentsByOrganization(
    organizationId,
    filters = {},
    continuationToken = null,
    pageSize = 20,
  ) {
    try {
      const query = paymentQuery.getPaymentsByOrganizationQuery(
        organizationId,
        filters,
        continuationToken,
      )
      const result = await cosmosDbContext.getAllItemQueryWithPagination(
        paymentsContainer,
        query,
        pageSize,
        continuationToken,
      )
      return result
    } catch (error) {
      logging.logError('Error fetching payments by organization', error)
      throw new Error(
        `Failed to fetch payments by organization: ${error.message}`,
      )
    }
  }

  async getPaymentStatistics(organizationId, startDate = null, endDate = null) {
    try {
      const query = paymentQuery.getPaymentStatisticsQuery(
        organizationId,
        startDate,
        endDate,
      )
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  async getPaymentBySubscriberEmail(organizationId, subscriberEmail) {
    try {
      const query = paymentQuery.getPaymentBySubscriberEmailQuery(organizationId, subscriberEmail)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result : []
    } catch (error) {
      logging.logError('Error fetching payment by subscriber email', error)
      throw new Error(
        `Failed to fetch payment by subscriber email: ${error.message}`,
      )
    }
  }
}

module.exports = new PaymentRepository()
