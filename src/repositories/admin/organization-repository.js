const cosmosDbContext = require('../../cosmosDbContext/comosdb-context')
const { paginate } = require('../../utils/pagination')
const {
  getAllOrganizationsQuery,
  getOrganizationByIdQuery,
  getOrganizationByNameQuery,
  getOrganizationByEmailQuery,
} = require('../../queries/organization-query')
const organizationContainer = 'Organizations'

class OrganizationRepository {
  async createOrganization(data) {
    return cosmosDbContext.createItem(data, organizationContainer)
  }

  async updateOrganization(data) {
    return cosmosDbContext.upsertItem(data.id, data, organizationContainer)
  }

  async getAllOrganizations(nameFilter, pageSize, pageNumber) {
    const query = getAllOrganizationsQuery(nameFilter)
    const allItems = await cosmosDbContext.queryItems(
      query,
      organizationContainer,
    )

    const paginatedResult = paginate(allItems, pageSize, pageNumber)

    return {
      items: paginatedResult.items,
      totalCount: paginatedResult.totalItemCount,
      totalPages: Math.ceil(paginatedResult.totalItemCount / pageSize),
    }
  }

  async getOrganizationById(id) {
    const query = getOrganizationByIdQuery(id)
    const result = await cosmosDbContext.queryItems(
      query,
      organizationContainer,
    )
    return result.length > 0 ? result[0] : null
  }

  async getOrganizationByName(name) {
    const query = getOrganizationByNameQuery(name)
    const result = await cosmosDbContext.queryItems(
      query,
      organizationContainer,
    )
    return result.length > 0 ? result[0] : null
  }

  async getOrganizationByEmail(email) {
    const query = getOrganizationByEmailQuery(email)
    const result = await cosmosDbContext.queryItems(
      query,
      organizationContainer,
    )
    return result.length > 0 ? result[0] : null
  }

  async deleteOrganization(organizationId) {
    const organization = await this.getOrganizationById(organizationId)
    if (!organization) {
      throw new Error(
        `Entity with ID ${organizationId} does not exist in the system.`,
      )
    }

    return cosmosDbContext.deleteItem(
      organizationId,
      organization.id,
      organizationContainer,
    )
  }
}

module.exports = new OrganizationRepository()
