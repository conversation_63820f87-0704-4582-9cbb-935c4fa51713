const logging = require('../common/logging')
const paymentRepository = require('../repositories/payment-repository')
const patientRepository = require('../repositories/patient-repository')
const doctorService = require('./doctor-service')

/**
 * Invoice Service
 * Business logic for invoice management
 * Note: Invoices are essentially payments with enriched patient/doctor/bill details
 */
class InvoiceService {
  /**
   * Get all invoices for an organization with filters, search, and pagination
   */
  async getInvoicesByOrganization({
    organizationId,
    page = 1,
    limit = 20,
    searchTerm,
    paymentType,
    startDate,
    endDate,
    gender,
  }) {
    try {
      
      const filters = {}
      if (paymentType) filters.paymentType = paymentType
      if (startDate) filters.startDate = startDate
      if (endDate) filters.endDate = endDate

      logging.logInfo(
        `Fetching invoices for organizationId: ${organizationId}, filters: ${JSON.stringify(filters)}`,
      )

      const result = await paymentRepository.getPaymentsByOrganization(
        organizationId,
        filters,
        null,
        1000, // Fetch all, we'll paginate in memory
      )

      const payments = result.items || result.resources || []

      logging.logInfo(`Total payments fetched: ${payments.length}`)

      const enrichedInvoices = await this.enrichInvoices(payments)

      let filteredInvoices = enrichedInvoices
      if (searchTerm && searchTerm.trim() !== '') {
        filteredInvoices = this.searchInvoices(enrichedInvoices, searchTerm)
      }
     
      if (gender) {
        filteredInvoices = filteredInvoices.filter(
          (invoice) => invoice.gender === gender,
        )
      }

      filteredInvoices.sort((a, b) => {
        const dateA = new Date(a.rawPayment.createdAt || 0)
        const dateB = new Date(b.rawPayment.createdAt || 0)
        return dateB - dateA
      })

      
      const paginatedResult = this.paginateInvoices(
        filteredInvoices,
        page,
        limit,
      )

      return {
        success: true,
        data: paginatedResult,
      }
    } catch (error) {
      logging.logError('Error in getInvoicesByOrganization:', error)
      throw new Error(
        `Failed to fetch invoices: ${error.message || 'Unknown error'}`,
      )
    }
  }

  /**
   * Get invoice by ID with full details
   */
  async getInvoiceById(invoiceId) {
    try {
      const payment = await paymentRepository.getPaymentById(invoiceId)

      if (!payment) {
        return {
          success: false,
          message: 'Invoice not found',
        }
      }

      let patientDetails = null
      if (payment.patientId) {
        try {
          patientDetails = await patientRepository.getPatientById(
            payment.patientId,
          )
        } catch (error) {
          logging.logError('Error fetching patient for invoice:', error)
        }
      }

      let doctorDetails = null
      const doctorId = payment.metadata?.doctorId || payment.notes?.doctorId
      if (doctorId) {
        try {
          doctorDetails = await doctorService.getDoctorById(doctorId)
        } catch (error) {
          logging.logError('Error fetching doctor for invoice:', error)
        }
      }

      let billDetails = null
      try {
        switch (payment.paymentType) {
          case 'consultation':
            billDetails = await this.getConsultationBillDetails(payment)
            break
          case 'pharmacy':
            billDetails = await this.getPrescriptionBillDetails(payment)
            break
          case 'lab_test':
            billDetails = await this.getLabTestBillDetails(payment)
            break
          case 'subscription':
            billDetails = await this.getSubscriptionBillDetails(payment)
            break
          default:
            billDetails = null
        }
      } catch (error) {
        logging.logError('Error fetching bill details:', error)
      }

      const invoice = {
        ...payment,
        amountInRupees: payment.amount / 100,
        patient: patientDetails,
        doctor: doctorDetails,
        billDetails,
      }

      return {
        success: true,
        data: invoice,
      }
    } catch (error) {
      logging.logError('Error in getInvoiceById:', error)
      throw new Error(
        `Failed to fetch invoice: ${error.message || 'Unknown error'}`,
      )
    }
  }

  /**
   * Enrich multiple invoices with patient and doctor details
   */
  async enrichInvoices(payments) {
    return Promise.all(payments.map((payment) => this.enrichInvoice(payment)))
  }

  /**
   * Enrich a single invoice with patient and doctor details
   */
  async enrichInvoice(payment) {
    let patientName = 'N/A'
    let patientId = payment.patientId || '-'
    let patientGender = 'N/A'
    
    if (payment.patientId) {
      try {
        const patient = await patientRepository.getPatientById(
          payment.patientId,
        )
        if (patient) {
          patientName = patient.name || 'N/A'
          patientGender = patient.gender || 'N/A'
        }
      } catch (error) {
        logging.logError('Error fetching patient for invoice:', error)
      }
    }

    let doctorName = 'N/A'

    const doctorId = payment.metadata?.doctorId || payment.notes?.doctorId
    if (doctorId) {
      try {
        const doctor = await doctorService.getDoctorById(doctorId)
        if (doctor) {
          doctorName = doctor.name || 'N/A'
        }
      } catch (error) {
        logging.logError('Error fetching doctor for invoice:', error)
      }
    }

    let subscriberEmail = 'N/A'
    if (payment.paymentType === 'subscription' && payment.subscriberEmail) {
      subscriberEmail = payment.subscriberEmail
    }

    return {
      date: payment.createdAt
        ? new Date(payment.createdAt).toLocaleDateString('en-GB')
        : 'N/A',
      type: payment.paymentType || 'N/A',
      patientId: patientId,
      fullName: patientName,
      gender: patientGender,
      amount: payment.amount / 100, // Convert from paise to rupees
      modeOfPayment: payment.notes?.paymentMethod || 'Card',
      transactionId: payment.razorpayPaymentId || '-',
      doctorName: doctorName,
      subscriberEmail: subscriberEmail,
      paymentId: payment.id,
      status: payment.status,
      rawPayment: payment,
    }
  }

  /**
   * Search invoices by multiple fields
   */
  searchInvoices(invoices, searchTerm) {
    const searchLower = searchTerm.toLowerCase()
    return invoices.filter(
      (invoice) =>
        invoice.fullName.toLowerCase().includes(searchLower) ||
        invoice.patientId.toLowerCase().includes(searchLower) ||
        invoice.transactionId.toLowerCase().includes(searchLower) ||
        (invoice.subscriberEmail &&
          invoice.subscriberEmail.toLowerCase().includes(searchLower)),
    )
  }

  /**
   * Paginate invoices
   */
  paginateInvoices(invoices, page, limit) {
    const totalCount = invoices.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedInvoices = invoices.slice(startIndex, endIndex)

    return {
      invoices: paginatedInvoices,
      total: totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit),
    }
  }

  /**
   * Get consultation bill details
   */
  async getConsultationBillDetails(payment) {
    try {
      return {
        type: 'consultation',
        consultationFee: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
      }
    } catch (error) {
      logging.logError('Error getting consultation bill details:', error)
      return null
    }
  }

  /**
   * Get prescription (pharmacy) bill details
   */
  async getPrescriptionBillDetails(payment) {
    try {
      const prescriptionRepository = require('../repositories/prescription-repository')

      const prescriptionId =
        payment.metadata?.prescriptionId || payment.notes?.prescriptionId

      if (!prescriptionId) {
        return {
          type: 'pharmacy',
          items: [],
          totalAmount: payment.amount / 100,
        }
      }

      const prescription =
        await prescriptionRepository.getPrescriptionById(prescriptionId)

      if (!prescription || !prescription.medicines) {
        return {
          type: 'pharmacy',
          items: [],
          totalAmount: payment.amount / 100,
        }
      }

      
      const items = prescription.medicines.map((med, index) => ({
        no: index + 1,
        drugName: med.drugName || med.name || 'N/A',
        genericName: med.genericName || 'N/A',
        brandName: med.brandName || med.drugName || 'N/A',
        strength: med.strength || 'N/A',
        qty: med.quantity || 1,
        cost: med.price || 0,
      }))

      return {
        type: 'pharmacy',
        items,
        totalAmount: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
      }
    } catch (error) {
      logging.logError('Error getting prescription bill details:', error)
      return {
        type: 'pharmacy',
        items: [],
        totalAmount: payment.amount / 100,
      }
    }
  }

  /**
   * Get lab test bill details
   */
  async getLabTestBillDetails(payment) {
    try {
      const labTestRepository = require('../repositories/lab-test-repository')

      const labTestId = payment.metadata?.labTestId || payment.notes?.labTestId

      if (!labTestId) {
        return {
          type: 'lab_test',
          tests: [],
          totalAmount: payment.amount / 100,
        }
      }

      const labTest = await labTestRepository.getLabTestById(labTestId)

      if (!labTest || !labTest.tests) {
        return {
          type: 'lab_test',
          tests: [],
          totalAmount: payment.amount / 100,
        }
      }

      const tests = labTest.tests.map((test) => ({
        department: test.department || 'N/A',
        testName: test.testName || test.name || 'N/A',
        subTests: test.subTests || [],
        amount: test.price || test.amount || 0,
      }))

      return {
        type: 'lab_test',
        tests,
        totalAmount: payment.amount / 100,
        labTechnician:
          labTest.labTechnician || payment.metadata?.labTechnician || 'N/A',
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedBy: payment.metadata?.billedBy || payment.created_by_name,
        billedAt: payment.createdAt,
      }
    } catch (error) {
      logging.logError('Error getting lab test bill details:', error)
      return {
        type: 'lab_test',
        tests: [],
        totalAmount: payment.amount / 100,
      }
    }
  }

  /**
   * Get subscription bill details
   */
  async getSubscriptionBillDetails(payment) {
    try {
      return {
        type: 'subscription',
        subscriberEmail: payment.subscriberEmail || 'N/A',
        subscriptionAmount: payment.amount / 100,
        paymentMethod: payment.notes?.paymentMethod || 'Card',
        billedAt: payment.createdAt,
        description: payment.description || 'Subscription Payment',
      }
    } catch (error) {
      logging.logError('Error getting subscription bill details:', error)
      return {
        type: 'subscription',
        totalAmount: payment.amount / 100,
      }
    }
  }
}

module.exports = new InvoiceService()
