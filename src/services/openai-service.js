const { OpenAI } = require('openai')
const { Configuration, OpenAIApi } = require('azure-openai')
const helper = require('../common/helper')
const secretManager = require('./secret-manager')
const logging = require('../common/logging')

// Initialize OpenAI clients with secrets from Key Vault
let openai = null
let azureOpenAI = null
let model = null
let isInitialized = false
let initializationPromise = null

async function initializeOpenAI() {
  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = _doInitialize()
  return initializationPromise
}

async function _doInitialize() {
  try {
    logging.logInfo('Initializing OpenAI with Key Vault secrets...')

    // Get secrets from Key Vault
    const url = await secretManager.getSecret('OPENAI_ENDPOINT')
    const key = await secretManager.getSecret('OPENAI_KEY')
    const model = await secretManager.getSecret('OPENAI_MODEL')

    if (!url || !key) {
      throw new Error(
        'Missing OpenAI credentials in Key Vault or environment variables',
      )
    }

    openai = new OpenAI({
      baseURL: url,
      apiKey: key,
    })

    azureOpenAI = new OpenAIApi(
      new Configuration({
        apiKey: key,
        azure: {
          apiKey: key,
          endpoint: url,
          deploymentName: model,
        },
      }),
    )

    isInitialized = true
    logging.logInfo('OpenAI initialized successfully')
  } catch (error) {
    logging.logError('Failed to initialize OpenAI:', error)
    isInitialized = false
    throw error
  }
}

class OpenAIService {
  // Estimate token count (rough approximation: 1 token ≈ 4 characters)
  estimateTokens(text) {
    return Math.ceil(text.length / 4)
  }

  // Get safe token limits based on deployment
  async getTokenLimits() {
    if (!isInitialized) {
      await initializeOpenAI()
    }

    const currentModel = model || 'gpt-35-turbo'

    // Conservative limits to avoid 500 errors
    if (currentModel.includes('gpt-4-32k') || currentModel.includes('32k')) {
      return { maxContext: 30000, maxCompletion: 8000 }
    } else if (currentModel.includes('gpt-4') || currentModel.includes('4o')) {
      return { maxContext: 6000, maxCompletion: 4000 }
    } else {
      // GPT-3.5 or unknown models - be very conservative
      return { maxContext: 3000, maxCompletion: 2000 }
    }
  }

  // Split large prompts into smaller chunks
  chunkPrompt(prompt, maxChunkSize = 8000) {
    if (prompt.length <= maxChunkSize) {
      return [prompt]
    }

    const chunks = []
    let currentChunk = ''

    // Split by sentences to maintain context
    const sentences = prompt.split(/(?<=[.!?])\s+/)

    for (const sentence of sentences) {
      if (
        (currentChunk + sentence).length > maxChunkSize &&
        currentChunk.length > 0
      ) {
        chunks.push(currentChunk.trim())
        currentChunk = sentence
      } else {
        currentChunk += (currentChunk ? ' ' : '') + sentence
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim())
    }

    return chunks
  }

  async chatCompletion(classification, prompt, maxRetries = 2) {
    // Ensure OpenAI is initialized
    if (!isInitialized) {
      await initializeOpenAI()
    }

    const limits = await this.getTokenLimits()
    const promptTokens = this.estimateTokens(classification + prompt)

    logging.logInfo(
      `Prompt length: ${prompt.length} chars, estimated tokens: ${promptTokens}`,
    )

    // If prompt is too large, use chunking strategy
    // Use a more conservative threshold to ensure complete responses
    const chunkingThreshold = limits.maxContext * 0.6 // Use 60% of context as threshold

    if (promptTokens > chunkingThreshold) {
      logging.logInfo(
        `Prompt tokens (${promptTokens}) exceed chunking threshold (${chunkingThreshold}), using chunking strategy`,
      )

      // Check if this is conversation parsing and if we need smart chunking
      if (
        classification.includes('identify which parts are spoken by the doctor')
      ) {
        // For very long conversations, use smart conversation chunking
        if (prompt.length > 15000) {
          // 15k characters threshold for smart chunking
          logging.logInfo(
            `Conversation is very long (${prompt.length} chars), using smart conversation chunking`,
          )
          return await this.processConversationWithSmartChunking(
            prompt,
            classification,
            limits,
            maxRetries,
          )
        }
      }

      // Check if this is a summary generation request
      if (
        classification.includes(
          'analyze and return detailed information in JSON format',
        )
      ) {
        logging.logInfo('Using summary-specific chunking strategy')
        return await this.chatCompletionSummaryChunking(
          classification,
          prompt,
          maxRetries,
        )
      } else {
        return await this.chatCompletionWithChunking(
          classification,
          prompt,
          maxRetries,
        )
      }
    }

    return await this.chatCompletionSingle(classification, prompt, maxRetries)
  }

  async chatCompletionSingle(classification, prompt, maxRetries = 2) {
    const limits = this.getTokenLimits()
    let lastError = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        var messages = [
          { role: 'system', content: `${classification}` },
          { role: 'user', content: `${prompt}` },
        ]

        // Calculate safe max_tokens
        const promptTokens = this.estimateTokens(classification + prompt)

        const availableTokens = limits.maxContext - promptTokens - 100 // Leave 100 token buffer

        // Optimized token allocation based on content length and model
        let maxTokens
        const model = await secretManager.getSecret('OPENAI_MODEL')

        if (
          classification.includes(
            'identify which parts are spoken by the doctor',
          )
        ) {
          // Conversation parsing - ALWAYS ensure enough tokens for complete JSON array
          // For very long/complex payloads, we need even more aggressive token allocation
          const estimatedOutputTokens = Math.max(promptTokens * 1.5, 3000) // Increased to 150% of input size, minimum 3000

          // Dynamic minimum based on input complexity
          let minTokens
          if (model.includes('4o')) {
            // For GPT-4o: scale minimum tokens based on input size
            minTokens = promptTokens > 8000 ? 6000 : 4000 // Higher minimum for very long inputs
          } else {
            minTokens = promptTokens > 6000 ? 4500 : 3000 // Higher minimum for very long inputs
          }

          // For conversation parsing, prioritize completion over context limits
          // Use the higher of minimum tokens or estimated output tokens
          const requiredTokens = Math.max(minTokens, estimatedOutputTokens)
          maxTokens = Math.min(limits.maxCompletion, requiredTokens)

          // If we still don't have enough tokens, use maximum available
          if (maxTokens < minTokens) {
            maxTokens = Math.min(limits.maxCompletion, minTokens)
          }

          // For extremely long inputs, ensure we use at least 75% of max completion tokens
          if (promptTokens > 10000) {
            const aggressiveMinimum = Math.floor(limits.maxCompletion * 0.75)
            maxTokens = Math.max(maxTokens, aggressiveMinimum)
          }

          // For very complex/long inputs, use maximum available tokens
          if (promptTokens > 8000) {
            maxTokens = Math.min(
              limits.maxCompletion,
              Math.max(maxTokens, limits.maxCompletion * 0.9),
            )
          }

          // Final safety check - ensure we have substantial tokens for conversation parsing
          const absoluteMinimum = model.includes('4o') ? 6000 : 4000
          maxTokens = Math.max(maxTokens, absoluteMinimum)
        } else {
          // Summary generation - needs substantial tokens for complete medical summary JSON
          const minTokens = model.includes('4o') ? 5000 : 4000
          maxTokens = Math.min(
            limits.maxCompletion,
            Math.max(minTokens, availableTokens),
          )
        }

        logging.logInfo(
          `Token allocation - Input: ${promptTokens}, Max completion: ${limits.maxCompletion}, Allocated: ${maxTokens}, Model: ${model}`,
        )

        var result = await azureOpenAI.createChatCompletion({
          messages: messages,
          temperature: 0,
          top_p: 0,
          max_tokens: maxTokens,
        })

        var data = result.data.choices[0].message.content
        // Validate that we got a reasonable response
        if (!data || data.trim().length === 0) {
          throw new Error('Empty response from OpenAI')
        }

        // Check if this is conversation parsing and if the response is incomplete
        if (
          classification.includes(
            'identify which parts are spoken by the doctor',
          )
        ) {
          // Check if the JSON ends with an incomplete object
          const trimmedResponse = data.trim()
          if (
            trimmedResponse.endsWith('{"speaker":"patient"}') ||
            trimmedResponse.endsWith('{"speaker":"doctor"}') ||
            trimmedResponse.endsWith('{"speaker":"patient"') ||
            trimmedResponse.endsWith('{"speaker":"doctor"') ||
            trimmedResponse.endsWith('{"speaker":') ||
            (trimmedResponse.includes('{"speaker":') &&
              !trimmedResponse.endsWith(']'))
          ) {
            logging.logInfo(
              `Detected incomplete conversation JSON, retrying with maximum tokens`,
            )

            // Retry with maximum available tokens
            const retryMaxTokens = limits.maxCompletion

            const retryResult = await azureOpenAI.createChatCompletion({
              messages: messages,
              temperature: 0,
              top_p: 0,
              max_tokens: retryMaxTokens,
            })

            const retryData = retryResult.data.choices[0].message.content
            logging.logInfo(
              `Retry completed with ${retryMaxTokens} tokens, response length: ${retryData.length}`,
            )

            // Use the retry response if it's more complete
            if (
              retryData.length > data.length &&
              (retryData.trim().endsWith(']') ||
                retryData.length > data.length * 1.2)
            ) {
              data = retryData
              logging.logInfo(
                `Using retry response as it appears more complete`,
              )
            } else {
              // If retry still didn't work, try one more time with absolute maximum tokens
              const finalTrimmedResponse = retryData.trim()
              if (
                finalTrimmedResponse.endsWith('{"speaker":"patient"}') ||
                finalTrimmedResponse.endsWith('{"speaker":"doctor"}') ||
                finalTrimmedResponse.endsWith('{"speaker":"patient"') ||
                finalTrimmedResponse.endsWith('{"speaker":"doctor"') ||
                finalTrimmedResponse.endsWith('{"speaker":"nurse"}') ||
                finalTrimmedResponse.endsWith('{"speaker":"nurse"') ||
                finalTrimmedResponse.endsWith('{"speaker":') ||
                (finalTrimmedResponse.includes('{"speaker":') &&
                  !finalTrimmedResponse.endsWith(']'))
              ) {
                logging.logInfo(
                  `Second retry still incomplete, attempting final retry with absolute maximum tokens`,
                )

                // Final attempt with absolute maximum tokens and modified prompt
                const finalMaxTokens = Math.min(limits.maxCompletion, 8000) // Use 8000 or max available

                const finalResult = await azureOpenAI.createChatCompletion({
                  messages: [
                    {
                      role: 'user',
                      content:
                        classification +
                        '\n\nIMPORTANT: This is a final attempt. You MUST complete the entire JSON array. Do not stop until you reach the end of the input text and close the JSON array with ]. Every object must have both speaker and message properties.\n\n' +
                        prompt,
                    },
                  ],
                  temperature: 0,
                  top_p: 0,
                  max_tokens: finalMaxTokens,
                })

                const finalData = finalResult.data.choices[0].message.content
                logging.logInfo(
                  `Final retry completed with ${finalMaxTokens} tokens, response length: ${finalData.length}`,
                )

                // Use the final response if it's better
                if (
                  finalData.length > data.length &&
                  finalData.length > retryData.length
                ) {
                  data = finalData
                  logging.logInfo(
                    `Using final retry response as it's the most complete`,
                  )
                } else if (retryData.length > data.length) {
                  data = retryData
                  logging.logInfo(
                    `Using first retry response as it's better than original`,
                  )
                }
              }
            }
          }
        }

        return data
      } catch (error) {
        lastError = error
        logging.logError(
          `OpenAI chat completion attempt ${attempt + 1} failed`,
          error,
        )

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * (attempt + 1)),
          ) // Exponential backoff
        }
      }
    }

    logging.logError(`All OpenAI chat completion attempts failed`, lastError)
    return null
  }

  async chatCompletionWithChunking(classification, prompt, maxRetries = 2) {
    try {
      const chunks = this.chunkPrompt(prompt, 8000)
      logging.logInfo(`Split prompt into ${chunks.length} chunks`)

      const results = []

      // Process each chunk
      for (let i = 0; i < chunks.length; i++) {
        logging.logInfo(`Processing chunk ${i + 1}/${chunks.length}`)

        // Modify classification for chunked processing
        const chunkClassification = classification.replace(
          'Return only the JSON array, nothing else.',
          `Return only the JSON array for this part of the conversation (chunk ${
            i + 1
          }/${chunks.length}), nothing else.`,
        )

        const chunkResult = await this.chatCompletionSingle(
          chunkClassification,
          chunks[i],
          maxRetries,
        )

        if (chunkResult) {
          results.push(chunkResult)
        } else {
          logging.logError(`Failed to process chunk ${i + 1}`)
          // Continue with other chunks even if one fails
        }
      }

      if (results.length === 0) {
        logging.logError('All chunks failed to process')
        return null
      }

      // Combine results
      return this.combineChunkResults(results)
    } catch (error) {
      logging.logError('Error in chunking strategy', error)
      return null
    }
  }

  combineChunkResults(results) {
    try {
      const allConversations = []
      const helper = require('../common/helper')

      for (const result of results) {
        if (!result || result.trim() === '') {
          continue
        }

        // Try multiple parsing strategies
        let parsed = null

        // Strategy 1: Direct JSON parsing
        try {
          parsed = JSON.parse(result)
        } catch (e) {
          // Strategy 2: Use our enhanced helper functions
          try {
            parsed = helper.parseJSON(result)
          } catch (e2) {
            // Strategy 3: Extract conversation objects manually
            try {
              parsed = helper.extractConversationObjects(result)
            } catch (e3) {
              logging.logError(
                `Failed to parse chunk result: ${result.substring(0, 200)}...`,
                e3,
              )
              continue
            }
          }
        }

        // Add parsed conversations to the collection
        if (Array.isArray(parsed)) {
          allConversations.push(...parsed)
        } else if (
          parsed &&
          typeof parsed === 'object' &&
          parsed.speaker &&
          parsed.message
        ) {
          allConversations.push(parsed)
        }
      }

      if (allConversations.length === 0) {
        logging.logError('No conversations could be parsed from chunks')
        return null
      }

      // Remove duplicates based on message content
      const uniqueConversations = []
      const seenMessages = new Set()

      for (const conv of allConversations) {
        const messageKey = `${conv.speaker}:${conv.message?.substring(0, 100)}`
        if (!seenMessages.has(messageKey)) {
          seenMessages.add(messageKey)
          uniqueConversations.push(conv)
        }
      }

      logging.logInfo(
        `Combined ${uniqueConversations.length} unique conversations from ${results.length} chunks`,
      )

      // Return combined result as JSON string
      return JSON.stringify(uniqueConversations)
    } catch (error) {
      logging.logError('Error combining chunk results', error)
      // Return the first successful result as fallback
      return results[0] || null
    }
  }

  async chatCompletionSummaryChunking(
    classification,
    conversationJson,
    maxRetries = 3,
  ) {
    try {
      // Parse the conversation JSON to get the array
      let conversation
      try {
        conversation = JSON.parse(conversationJson)
      } catch (e) {
        logging.logError('Failed to parse conversation JSON for summary', e)
        return null
      }

      if (!Array.isArray(conversation)) {
        logging.logError('Conversation is not an array for summary generation')
        return null
      }

      // For summary generation, we'll chunk the conversation array and create partial summaries
      const chunkSize = 50 // Process 30 conversation messages at a time
      const chunks = []

      for (let i = 0; i < conversation.length; i += chunkSize) {
        chunks.push(conversation.slice(i, i + chunkSize))
      }

      logging.logInfo(
        `Split conversation into ${chunks.length} chunks for summary generation`,
      )

      const partialSummaries = []

      // Generate partial summaries for each chunk
      for (let i = 0; i < chunks.length; i++) {
        logging.logInfo(`Processing summary chunk ${i + 1}/${chunks.length}`)

        const chunkJson = JSON.stringify(chunks[i])
        const partialSummary = await this.chatCompletionSingle(
          classification,
          chunkJson,
          maxRetries,
        )

        if (partialSummary) {
          partialSummaries.push(partialSummary)
        } else {
          logging.logError(`Failed to generate summary for chunk ${i + 1}`)
        }
      }

      if (partialSummaries.length === 0) {
        logging.logError('All summary chunks failed to process')
        return null
      }

      // If we only have one partial summary, return it
      if (partialSummaries.length === 1) {
        return partialSummaries[0]
      }

      // Combine partial summaries into a final comprehensive summary
      return await this.combineSummaries(
        partialSummaries,
        classification,
        maxRetries,
      )
    } catch (error) {
      logging.logError('Error in summary chunking strategy', error)
      return null
    }
  }

  async combineSummaries(
    partialSummaries,
    originalClassification,
    maxRetries = 2,
  ) {
    try {
      // Parse all partial summaries first
      const parsedSummaries = []
      for (const summary of partialSummaries) {
        try {
          const parsed = helper.parseJSON(summary)
          if (parsed && typeof parsed === 'object') {
            parsedSummaries.push(parsed)
          }
        } catch (e) {
          logging.logError('Error parsing partial summary:', e)
          continue
        }
      }

      if (parsedSummaries.length === 0) {
        logging.logError('No valid partial summaries to combine')
        return null
      }

      // Create a base summary structure
      const combinedSummary = helper.getDefaultSummary()

      // Combine list fields (HTML lists)
      const listFields = [
        'presentingcomplaint',
        'historyofpresenting',
        'pastmedicalhistory',
        'pastsurgicalhistory',
        'familyhistory',
        'addictionhistory',
        'diethistory',
        'physicalactivityhistory',
        'stresshistory',
        'sleephistory',
        'currentmedicationhistory',
        'heent',
      ]

      for (const field of listFields) {
        const allItems = new Set()
        for (const summary of parsedSummaries) {
          if (summary[field]) {
            const items = this.extractListItems(summary[field])
            items.forEach((item) => allItems.add(item))
          }
        }
        if (allItems.size > 0) {
          combinedSummary[field] =
            '<ul>' +
            Array.from(allItems)
              .map((item) => `<li>${item}</li>`)
              .join('') +
            '</ul>'
        }
      }

      // Combine vitals - use most recent non-zero values
      for (const summary of parsedSummaries) {
        if (summary.vitals) {
          for (const key in combinedSummary.vitals) {
            if (key === 'temperature') {
              if (summary.vitals[key]) {
                if (
                  typeof summary.vitals[key] === 'object' &&
                  summary.vitals[key].value &&
                  summary.vitals[key].value !== 0
                ) {
                  combinedSummary.vitals[key] = summary.vitals[key]
                } else if (
                  typeof summary.vitals[key] === 'number' &&
                  summary.vitals[key] !== 0
                ) {
                  combinedSummary.vitals[key] = {
                    value: summary.vitals[key],
                    unit: 'Celsius',
                  }
                }
              }
            } else {
              if (summary.vitals[key] && summary.vitals[key] !== 0) {
                combinedSummary.vitals[key] = summary.vitals[key]
              }
            }
          }
        }
      }

      // Combine anthropometry - use most recent non-empty values
      for (const summary of parsedSummaries) {
        if (summary.anthropometry) {
          for (const key in combinedSummary.anthropometry) {
            if (
              summary.anthropometry[key] &&
              summary.anthropometry[key] !== '' &&
              summary.anthropometry[key] !== 0
            ) {
              combinedSummary.anthropometry[key] = summary.anthropometry[key]
            }
          }
        }
      }

      // Combine general physical examination - use true if any summary has true
      for (const summary of parsedSummaries) {
        if (summary.generalphysicalexamination) {
          for (const key in combinedSummary.generalphysicalexamination) {
            if (typeof summary.generalphysicalexamination[key] === 'boolean') {
              combinedSummary.generalphysicalexamination[key] =
                combinedSummary.generalphysicalexamination[key] ||
                summary.generalphysicalexamination[key]
            } else if (summary.generalphysicalexamination[key]) {
              combinedSummary.generalphysicalexamination[key] =
                summary.generalphysicalexamination[key]
            }
          }
        }
      }

      // Combine systemic examination - merge all unique items in each section
      for (const summary of parsedSummaries) {
        if (summary.systemicexamination) {
          for (const key in combinedSummary.systemicexamination) {
            const items = this.extractListItems(
              summary.systemicexamination[key],
            )
            const existingItems = this.extractListItems(
              combinedSummary.systemicexamination[key],
            )
            const allItems = new Set([...existingItems, ...items])
            if (allItems.size > 0) {
              combinedSummary.systemicexamination[key] =
                '<ul>' +
                Array.from(allItems)
                  .map((item) => `<li>${item}</li>`)
                  .join('') +
                '</ul>'
            }
          }
        }
      }

      // Validate the combined summary
      if (!helper.validateMedicalRecord(combinedSummary)) {
        logging.logError('Combined summary validation failed')
        return null
      }

      return JSON.stringify(combinedSummary)
    } catch (error) {
      logging.logError('Error combining summaries:', error)
      return null
    }
  }

  // Helper method to extract list items from HTML list
  extractListItems(htmlList) {
    if (!htmlList) return []
    const items = new Set()
    const matches = htmlList.match(/<li>(.*?)<\/li>/g)
    if (matches) {
      matches.forEach((match) => {
        const item = match.replace(/<\/?li>/g, '').trim()
        if (item) items.add(item)
      })
    }
    return Array.from(items)
  }

  // Smart conversation chunking for very long conversations
  async processConversationWithSmartChunking(
    prompt,
    classification,
    limits,
    maxRetries,
  ) {
    try {
      logging.logInfo(
        `Starting smart conversation chunking for ${prompt.length} character conversation`,
      )

      // Split conversation into smart chunks based on natural conversation breaks
      const chunks = this.smartSplitConversation(prompt)
      logging.logInfo(`Split conversation into ${chunks.length} smart chunks`)

      const allConversations = []

      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i]
        logging.logInfo(
          `Processing conversation chunk ${i + 1}/${chunks.length} (${
            chunk.length
          } chars)`,
        )

        try {
          // Process each chunk with enhanced prompt for continuation
          const chunkClassification =
            classification +
            (i > 0
              ? '\n\nIMPORTANT: This is a continuation of a longer conversation. Process all messages in this segment and ensure the JSON array is complete.'
              : '') +
            '\n\nProcess EVERY message in this conversation segment. Do not stop until you reach the end of the text.'

          const chunkResult = await this.chatCompletionSingle(
            chunkClassification,
            chunk,
            maxRetries,
          )

          if (chunkResult) {
            // Parse the JSON response
            const parsedChunk = this.parseConversationResponse(chunkResult)
            if (parsedChunk && parsedChunk.length > 0) {
              allConversations.push(...parsedChunk)
              logging.logInfo(
                `Chunk ${i + 1} processed successfully: ${
                  parsedChunk.length
                } messages`,
              )
            } else {
              logging.logError(`Failed to parse chunk ${i + 1}`)
            }
          }
        } catch (error) {
          logging.logError(`Error processing chunk ${i + 1}:`, error)
          // Continue with other chunks even if one fails
        }
      }

      logging.logInfo(
        `Smart chunking completed: ${allConversations.length} total messages`,
      )

      // Return the combined conversations as JSON string
      return JSON.stringify(allConversations)
    } catch (error) {
      logging.logError('Error in smart conversation chunking:', error)
      throw error
    }
  }

  smartSplitConversation(text) {
    // Enhanced smart splitting strategy for medical conversations
    const chunks = []
    const maxChunkSize = 10000 // Reduced to 10k for better processing

    // Enhanced splitting patterns for medical conversations
    // Look for natural conversation boundaries, speaker changes, and medical dialogue patterns
    const conversationBoundaries = [
      /(?:doctor|patient|nurse):\s*/gi,
      /(?:good morning|good afternoon|hello|hi|thank you|thanks|bye|goodbye|see you|take care)[\s.,!?]*/gi,
      /(?:let me|let's|now|okay|alright|well|so)[\s.,!?]+/gi,
      /(?<=[.!?])\s+(?=[A-Z])/g, // Sentence boundaries
      /(?<=\w)\s+(?=(?:yes|no|okay|alright|sure|i see|i understand|that's|what|how|when|where|why|do you|have you|are you|can you|will you|should i)[\s.,!?])/gi,
    ]

    let workingText = text
    let bestSplits = []

    // Try each boundary pattern to find optimal split points
    for (const pattern of conversationBoundaries) {
      const matches = [...workingText.matchAll(pattern)]
      if (matches.length > 0) {
        bestSplits = bestSplits.concat(matches.map((m) => m.index))
      }
    }

    // Sort split points and remove duplicates
    bestSplits = [...new Set(bestSplits)].sort((a, b) => a - b)

    // If we have good split points, use them
    if (bestSplits.length > 0) {
      let lastIndex = 0
      let currentChunk = ''

      for (const splitIndex of bestSplits) {
        const segment = workingText.slice(lastIndex, splitIndex).trim()

        if (
          currentChunk.length + segment.length > maxChunkSize &&
          currentChunk.length > 0
        ) {
          chunks.push(currentChunk.trim())
          currentChunk = segment
        } else {
          currentChunk += (currentChunk ? ' ' : '') + segment
        }

        lastIndex = splitIndex
      }

      // Add remaining text
      const remaining = workingText.slice(lastIndex).trim()
      if (remaining) {
        if (
          currentChunk.length + remaining.length > maxChunkSize &&
          currentChunk.length > 0
        ) {
          chunks.push(currentChunk.trim())
          chunks.push(remaining)
        } else {
          currentChunk += (currentChunk ? ' ' : '') + remaining
          chunks.push(currentChunk.trim())
        }
      } else if (currentChunk.trim()) {
        chunks.push(currentChunk.trim())
      }
    } else {
      // Fallback to sentence-based splitting
      const sentences = text.split(/(?<=[.!?])\s+/)
      let currentChunk = ''

      for (const sentence of sentences) {
        if (
          currentChunk.length + sentence.length > maxChunkSize &&
          currentChunk.length > 0
        ) {
          chunks.push(currentChunk.trim())
          currentChunk = sentence
        } else {
          currentChunk += (currentChunk ? ' ' : '') + sentence
        }
      }

      if (currentChunk.trim()) {
        chunks.push(currentChunk.trim())
      }
    }

    // Final pass: ensure no chunk is too large
    const finalChunks = []
    for (const chunk of chunks) {
      if (chunk.length > maxChunkSize) {
        // Split oversized chunks by words
        const words = chunk.split(' ')
        let subChunk = ''

        for (const word of words) {
          if (
            subChunk.length + word.length + 1 > maxChunkSize &&
            subChunk.length > 0
          ) {
            finalChunks.push(subChunk.trim())
            subChunk = word
          } else {
            subChunk += (subChunk ? ' ' : '') + word
          }
        }

        if (subChunk.trim()) {
          finalChunks.push(subChunk.trim())
        }
      } else if (chunk.trim()) {
        finalChunks.push(chunk.trim())
      }
    }

    return finalChunks.length > 0 ? finalChunks : [text] // Ensure we always return at least one chunk
  }

  parseConversationResponse(response) {
    try {
      // Clean the response - remove markdown formatting if present
      let cleanResponse = response.trim()

      // Remove markdown code blocks
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse
          .replace(/^```json\s*/, '')
          .replace(/\s*```$/, '')
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse
          .replace(/^```\s*/, '')
          .replace(/\s*```$/, '')
      }

      // Parse JSON
      const parsed = JSON.parse(cleanResponse)

      // Validate that it's an array of conversation objects
      if (Array.isArray(parsed)) {
        return parsed.filter(
          (item) =>
            item &&
            typeof item === 'object' &&
            item.speaker &&
            item.message &&
            typeof item.speaker === 'string' &&
            typeof item.message === 'string',
        )
      }

      return []
    } catch (error) {
      logging.logError('Error parsing conversation response:', error)
      return []
    }
  }
}

module.exports = new OpenAIService()
