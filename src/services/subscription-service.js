const subscriptionRepository = require('../repositories/subscription-repository')
const paymentRepository = require('../repositories/payment-repository')
const featureRepository = require('../repositories/feature-repository')
const organizationRepository = require('../repositories/admin/organization-repository')
const roleRepository = require('../repositories/admin/role-repository')
const OrganizationModel = require('../models/organization-model')
const SubscriptionPlan = require('../models/subscription-model').SubscriptionPlan
const emailService = require('./email-service')
const b2cService = require('./b2c-service')
const userService = require('./user-service')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const { DefaultRoles } = require('../common/roles')
const logging = require('../common/logging')
const secretManager = require('./secret-manager')
const { v4: uuidv4 } = require('uuid')

const subscriptionContainer = 'organization_subscriptions'
const userContainer = 'Users'

class SubscriptionService {
  
  cleanFeatures(moduleFeatures) {
    if (!moduleFeatures || typeof moduleFeatures !== 'object') {
      return moduleFeatures
    }

    const cleanedModuleFeatures = {}

    for (const [moduleName, featureArray] of Object.entries(moduleFeatures)) {
      if (!Array.isArray(featureArray)) {
        cleanedModuleFeatures[moduleName] = featureArray
        continue
      }

      cleanedModuleFeatures[moduleName] = featureArray.map(feature => ({
        featureId: feature.featureId,
        monthlyAmount: feature.monthlyAmount || 0,
        yearlyAmount: feature.yearlyAmount || 0,
      }))
    }

    return cleanedModuleFeatures
  }

  async enrichFeaturesWithNames(moduleFeatures) {
    if (!moduleFeatures || typeof moduleFeatures !== 'object') {
      return moduleFeatures
    }

    const enrichedModuleFeatures = {}

    for (const [moduleName, featureArray] of Object.entries(moduleFeatures)) {
      if (!Array.isArray(featureArray)) {
        enrichedModuleFeatures[moduleName] = featureArray
        continue
      }

      const enrichedFeatures = await Promise.all(
        featureArray.map(async (feature) => {
          if (!feature.featureId) {
            return feature
          }

          try {
            const featureDetails = await featureRepository.getFeatureById(feature.featureId)
            return {
              featureId: feature.featureId,
              featureName: featureDetails?.featureName || null,
              description: featureDetails?.description || null,
              monthlyAmount: feature.monthlyAmount,
              yearlyAmount: feature.yearlyAmount,
            }
          } catch (error) {
            logging.logError(`Error fetching feature details for ${feature.featureId}:`, error)
            return {
              featureId: feature.featureId,
              featureName: null,
              description: null,
              monthlyAmount: feature.monthlyAmount,
              yearlyAmount: feature.yearlyAmount,
            }
          }
        })
      )

      enrichedModuleFeatures[moduleName] = enrichedFeatures
    }

    return enrichedModuleFeatures
  }

  // Helper method to validate feature IDs in module-based structure
  async validateModuleFeatures(moduleFeatures, featureType = 'feature') {
    if (!moduleFeatures || typeof moduleFeatures !== 'object') {
      return // No features to validate
    }

    const invalidFeatures = []

    for (const [moduleName, featureArray] of Object.entries(moduleFeatures)) {
      if (!Array.isArray(featureArray) || featureArray.length === 0) {
        continue // Empty arrays are valid
      }

      for (const feature of featureArray) {
        if (!feature.featureId) {
          throw new Error(`${moduleName} ${featureType} must have a featureId`)
        }

        // Check if feature exists in the features container
        const existingFeature = await featureRepository.getFeatureById(feature.featureId)
        if (!existingFeature) {
          invalidFeatures.push(`${moduleName}:${feature.featureId}`)
        } else if (!existingFeature.isActive) {
          throw new Error(`Feature ${feature.featureId} in ${moduleName} module is not active`)
        }
      }
    }

    if (invalidFeatures.length > 0) {
      throw new Error(`Invalid feature IDs: ${invalidFeatures.join(', ')}`)
    }
  }

  async createSubscriptionPlan(planData, userId) {
    try {
      // Validate that all feature IDs exist and are active (module-based)
      if (planData.features) {
        await this.validateModuleFeatures(planData.features, 'base feature')
      }

      if (planData.addOnFeatures) {
        await this.validateModuleFeatures(planData.addOnFeatures, 'add-on feature')
      }

      const plan = await subscriptionRepository.createPlan({
        ...planData,
        created_by: userId,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Subscription plan created successfully',
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in createSubscriptionPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to create subscription plan',
      }
    }
  }

  async replaceSubscriptionPlan(planId, planData, userId) {
    try {
      // Check if plan exists
      const existingPlan = await subscriptionRepository.getPlanById(planId)
      if (!existingPlan) {
        throw new Error('Subscription plan not found')
      }

      // PUT requires planName and validity
      if (!planData.planName || !planData.validity) {
        throw new Error('Plan name and validity are required for full replacement')
      }

      // Validate that all feature IDs exist and are active (module-based)
      if (planData.features) {
        await this.validateModuleFeatures(planData.features, 'base feature')
      }

      if (planData.addOnFeatures) {
        await this.validateModuleFeatures(planData.addOnFeatures, 'add-on feature')
      }

      // Full replacement - do NOT merge with existing data
      const plan = await subscriptionRepository.replacePlan(planId, {
        ...planData,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Subscription plan replaced successfully',
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in replaceSubscriptionPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to replace subscription plan',
      }
    }
  }

  async updateSubscriptionPlan(planId, planData, userId) {
    try {
      // Check if plan exists
      const existingPlan = await subscriptionRepository.getPlanById(planId)
      if (!existingPlan) {
        throw new Error('Subscription plan not found')
      }

      // Validate that all feature IDs exist and are active (if provided, module-based)
      if (planData.features) {
        await this.validateModuleFeatures(planData.features, 'base feature')
      }

      if (planData.addOnFeatures) {
        await this.validateModuleFeatures(planData.addOnFeatures, 'add-on feature')
      }

      // Partial update - merge with existing data
      const plan = await subscriptionRepository.updatePlan(planId, {
        ...planData,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Subscription plan updated successfully',
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in updateSubscriptionPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to update subscription plan',
      }
    }
  }

  async deleteSubscriptionPlan(planId) {
    try {
      // Check if plan exists
      const existingPlan = await subscriptionRepository.getPlanById(planId)
      if (!existingPlan) {
        throw new Error('Subscription plan not found')
      }

      // Check if any active subscriptions use this plan
      const activeSubscriptions = await subscriptionRepository.getSubscriptionsByStatus('active')
      const planInUse = activeSubscriptions.some((sub) => sub.planId === planId)

      if (planInUse) {
        throw new Error('Cannot delete plan with active subscriptions. Please deactivate it instead.')
      }

      await subscriptionRepository.deletePlan(planId)

      return {
        success: true,
        message: 'Subscription plan deleted successfully',
      }
    } catch (error) {
      logging.logError('Error in deleteSubscriptionPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to delete subscription plan',
      }
    }
  }

  async deactivateSubscriptionPlan(planId, userId) {
    try {
      const plan = await subscriptionRepository.updatePlan(planId, {
        isActive: false,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Subscription plan deactivated successfully',
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in deactivateSubscriptionPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to deactivate subscription plan',
      }
    }
  }

  async getSubscriptionPlan(planId) {
    try {
      const plan = await subscriptionRepository.getPlanById(planId)

      if (!plan) {
        return {
          success: false,
          message: 'Subscription plan not found',
        }
      }

      // Enrich plan with feature names
      const enrichedFeatures = await this.enrichFeaturesWithNames(plan.features)
      const enrichedAddOnFeatures = await this.enrichFeaturesWithNames(plan.addOnFeatures)

      const enrichedPlan = {
        ...plan,
        features: enrichedFeatures,
        addOnFeatures: enrichedAddOnFeatures,
      }

      return {
        success: true,
        data: enrichedPlan,
      }
    } catch (error) {
      logging.logError('Error in getSubscriptionPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to get subscription plan',
      }
    }
  }

  async getAllSubscriptionPlans(organizationId = null) {
    try {
      const plans = await subscriptionRepository.getAllPlans()

      // Enrich plans with feature names
      const enrichedPlans = await Promise.all(
        plans.map(async (plan) => {
          const enrichedFeatures = await this.enrichFeaturesWithNames(plan.features)
          const enrichedAddOnFeatures = await this.enrichFeaturesWithNames(plan.addOnFeatures)

          return {
            ...plan,
            features: enrichedFeatures,
            addOnFeatures: enrichedAddOnFeatures,
          }
        })
      )

      return {
        success: true,
        data: enrichedPlans,
        count: enrichedPlans.length,
      }
    } catch (error) {
      logging.logError('Error in getAllSubscriptionPlans:', error)
      return {
        success: false,
        message: error.message || 'Failed to get subscription plans',
      }
    }
  }

  async searchSubscriptionPlans(searchParams) {
    try {
      const plans = await subscriptionRepository.searchPlans(searchParams)

      // Enrich plans with feature names
      const enrichedPlans = await Promise.all(
        plans.map(async (plan) => {
          const enrichedFeatures = await this.enrichFeaturesWithNames(plan.features)
          const enrichedAddOnFeatures = await this.enrichFeaturesWithNames(plan.addOnFeatures)

          return {
            ...plan,
            features: enrichedFeatures,
            addOnFeatures: enrichedAddOnFeatures,
          }
        })
      )

      return {
        success: true,
        data: enrichedPlans,
        count: enrichedPlans.length,
      }
    } catch (error) {
      logging.logError('Error in searchSubscriptionPlans:', error)
      return {
        success: false,
        message: error.message || 'Failed to search subscription plans',
      }
    }
  }

  // Organization Plan Methods (special plan for organization-level settings)

  async createOrganizationPlan(planData, userId) {
    try {
      const plan = await subscriptionRepository.createOrganizationPlan({
        planName: planData.planName,
        description: planData.description,
        created_by: userId,
      })

      return {
        success: true,
        message: 'Organization plan created successfully',
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in createOrganizationPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to create organization plan',
      }
    }
  }

  async getOrganizationPlan() {
    try {
      const plan = await subscriptionRepository.getOrganizationPlan()

      if (!plan) {
        return {
          success: false,
          message: 'Organization plan not found',
        }
      }

      return {
        success: true,
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in getOrganizationPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to get organization plan',
      }
    }
  }

  async updateOrganizationPlan(planData, userId) {
    try {
      const plan = await subscriptionRepository.updateOrganizationPlan({
        planName: planData.planName,
        description: planData.description,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Organization plan updated successfully',
        data: plan,
      }
    } catch (error) {
      logging.logError('Error in updateOrganizationPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to update organization plan',
      }
    }
  }

  // Organization Subscription Management

  async subscribeOrganization(subscriptionData, userId) {
    try {
      // Validate plan exists
      const plan = await subscriptionRepository.getPlanById(subscriptionData.planId)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      if (!plan.isActive) {
        throw new Error('Subscription plan is not active')
      }

      // Check for existing active subscription
      const existingSubscription = await subscriptionRepository.getActiveSubscriptionByOrganization(
        subscriptionData.organizationId,
      )

      if (existingSubscription) {
        throw new Error('Organization already has an active subscription')
      }

      const billingType = subscriptionData.billingType ||
        (plan.validity === 'Monthly' ? 'monthly' : 'yearly')

      const isFreeTrial = !subscriptionData.paymentId

      // Calculate end date
      const startDate = new Date()
      const endDate = new Date(startDate)

      if (isFreeTrial) {
        // Free trial: Always 1 month validity regardless of billing type
        endDate.setMonth(endDate.getMonth() + 1)
      } else {
        // Paid subscription: Use billing type
        if (billingType === 'monthly') {
          endDate.setMonth(endDate.getMonth() + 1)
        } else {
          endDate.setFullYear(endDate.getFullYear() + 1)
        }
      }

      
      let totalAmount = 0

      if (isFreeTrial) {
        totalAmount = 0
      } else {
        Object.values(plan.features || {}).forEach(featureArray => {
          featureArray.forEach(feature => {
            totalAmount += billingType === 'monthly'
              ? (feature.monthlyAmount || 0)
              : (feature.yearlyAmount || 0)
          })
        })

        let selectedAddOns = subscriptionData.selectedAddOnFeatures || {}
        if (selectedAddOns && Object.keys(selectedAddOns).length > 0) {
          Object.values(selectedAddOns).forEach(featureArray => {
            if (Array.isArray(featureArray)) {
              featureArray.forEach(feature => {
                totalAmount += billingType === 'monthly'
                  ? (feature.monthlyAmount || 0)
                  : (feature.yearlyAmount || 0)
              })
            }
          })
        }
      }

      let selectedAddOns = subscriptionData.selectedAddOnFeatures || {}

      const cleanedBaseFeatures = this.cleanFeatures(plan.features)
      const cleanedAddOnFeatures = this.cleanFeatures(selectedAddOns)

      const { SubscriptionType } = require('../common/constant')
      const subscription = await subscriptionRepository.createOrganizationSubscription({
        organizationId: subscriptionData.organizationId,
        planId: plan.id,
        planName: plan.planName,
        validity: plan.validity,
        billingType: billingType,
        features: cleanedBaseFeatures,
        addOnFeatures: cleanedAddOnFeatures, // Only selected add-ons
        status: subscriptionData.paymentId ? 'active' : 'pending',
        subscriptionType: SubscriptionType.ORGANIZATION,
        isFreeTrial: isFreeTrial,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        autoRenew: subscriptionData.autoRenew !== undefined ? subscriptionData.autoRenew : false,
        totalAmount,
        paymentMethod: subscriptionData.paymentMethod || 'razorpay',
        paymentId: subscriptionData.paymentId || null,
        billingDetails: subscriptionData.billingDetails || {},
        created_by: userId,
      })

      return {
        success: true,
        message: 'Organization subscribed successfully',
        data: subscription,
      }
    } catch (error) {
      logging.logError('Error in subscribeOrganization:', error)
      return {
        success: false,
        message: error.message || 'Failed to subscribe organization',
      }
    }
  }

  async updateOrganizationSubscription(subscriptionId, organizationId, updateData, userId) {
    try {
      const subscription = await subscriptionRepository.updateOrganizationSubscription(
        subscriptionId,
        organizationId,
        {
          ...updateData,
          updated_by: userId,
        },
      )

      return {
        success: true,
        message: 'Organization subscription updated successfully',
        data: subscription,
      }
    } catch (error) {
      logging.logError('Error in updateOrganizationSubscription:', error)
      return {
        success: false,
        message: error.message || 'Failed to update organization subscription',
      }
    }
  }

  async getOrganizationActiveSubscription(organizationId) {
    try {
      const subscription = await subscriptionRepository.getActiveSubscriptionByOrganization(organizationId)

      if (!subscription) {
        return {
          success: false,
          message: 'No active or pending subscription found for this organization',
        }
      }

      return {
        success: true,
        data: subscription,
      }
    } catch (error) {
      logging.logError('Error in getOrganizationActiveSubscription:', error)
      return {
        success: false,
        message: error.message || 'Failed to get organization subscription',
      }
    }
  }

  async getOrganizationSubscriptionHistory(organizationId) {
    try {
      const subscriptions = await subscriptionRepository.getOrganizationSubscriptionHistory(organizationId)

      return {
        success: true,
        data: subscriptions,
        count: subscriptions.length,
      }
    } catch (error) {
      logging.logError('Error in getOrganizationSubscriptionHistory:', error)
      return {
        success: false,
        message: error.message || 'Failed to get subscription history',
      }
    }
  }

  async cancelOrganizationSubscription(subscriptionId, organizationId, userId) {
    try {
      const subscription = await subscriptionRepository.cancelOrganizationSubscription(
        subscriptionId,
        organizationId,
      )

      return {
        success: true,
        message: 'Subscription cancelled successfully',
        data: subscription,
      }
    } catch (error) {
      logging.logError('Error in cancelOrganizationSubscription:', error)
      return {
        success: false,
        message: error.message || 'Failed to cancel subscription',
      }
    }
  }

  async renewOrganizationSubscription(organizationId, userId) {
    try {
      // Get current active subscription
      const currentSubscription = await subscriptionRepository.getActiveSubscriptionByOrganization(organizationId)

      if (!currentSubscription) {
        throw new Error('No active subscription found to renew')
      }

      // Get the plan details
      const plan = await subscriptionRepository.getPlanById(currentSubscription.planId)

      if (!plan || !plan.isActive) {
        throw new Error('Subscription plan is no longer available')
      }

      // Mark current subscription as expired
      await subscriptionRepository.updateOrganizationSubscription(
        currentSubscription.id,
        organizationId,
        { status: 'expired' },
      )

      // Create new subscription
      const startDate = new Date()
      const endDate = new Date(startDate)

      if (currentSubscription.billingType === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1)
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1)
      }

      const cleanedBaseFeatures = this.cleanFeatures(plan.features)
      const cleanedAddOnFeatures = this.cleanFeatures(currentSubscription.addOnFeatures)

      const newSubscription = await subscriptionRepository.createOrganizationSubscription({
        organizationId,
        planId: plan.id,
        planName: plan.planName,
        validity: plan.validity,
        billingType: currentSubscription.billingType,
        status: 'active',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        autoRenew: currentSubscription.autoRenew,
        features: cleanedBaseFeatures,
        addOnFeatures: cleanedAddOnFeatures,
        totalAmount: currentSubscription.totalAmount,
        paymentMethod: currentSubscription.paymentMethod,
        billingDetails: currentSubscription.billingDetails,
        created_by: userId,
      })

      return {
        success: true,
        message: 'Subscription renewed successfully',
        data: newSubscription,
      }
    } catch (error) {
      logging.logError('Error in renewOrganizationSubscription:', error)
      return {
        success: false,
        message: error.message || 'Failed to renew subscription',
      }
    }
  }

  async checkFeatureAccess(organizationId, featureName) {
    try {
      const subscription = await subscriptionRepository.getActiveSubscriptionByOrganization(organizationId)

      if (!subscription) {
        return {
          success: false,
          hasAccess: false,
          message: 'No active subscription found',
        }
      }

      // Check in base features
      let feature = subscription.baseFeatures?.find((f) => f.featureName === featureName)

      // Check in add-on features if not found in base
      if (!feature) {
        feature = subscription.addOnFeatures?.find((f) => f.featureName === featureName)
      }

      if (!feature) {
        return {
          success: true,
          hasAccess: false,
          message: 'Feature not included in subscription',
        }
      }

      return {
        success: true,
        hasAccess: true,
        feature,
        message: 'Feature access granted',
      }
    } catch (error) {
      logging.logError('Error in checkFeatureAccess:', error)
      return {
        success: false,
        hasAccess: false,
        message: error.message || 'Failed to check feature access',
      }
    }
  }

  async getUpcomingRenewals(daysAhead = 30, organizationId = null) {
    try {
      const renewals = await subscriptionRepository.getUpcomingRenewals(daysAhead, organizationId)

      return {
        success: true,
        data: renewals,
        count: renewals.length,
      }
    } catch (error) {
      logging.logError('Error in getUpcomingRenewals:', error)
      return {
        success: false,
        message: error.message || 'Failed to get upcoming renewals',
      }
    }
  }

  async getSubscriptionAnalytics(organizationId = null) {
    try {
      const analytics = await subscriptionRepository.getSubscriptionAnalytics(organizationId)
      const revenueByPlan = await subscriptionRepository.getRevenueByPlan()

      return {
        success: true,
        data: {
          summary: analytics,
          revenueByPlan,
        },
      }
    } catch (error) {
      logging.logError('Error in getSubscriptionAnalytics:', error)
      return {
        success: false,
        message: error.message || 'Failed to get subscription analytics',
      }
    }
  }

  // Helper function to deactivate users for a subscription (hard delete B2C, soft delete DB)
  async deactivateSubscriptionUsers(subscription, token) {
    const graphService = require('./graph-service')
    const userRepository = require('../repositories/admin/user-repository')
    const { SubscriptionType } = require('../common/constant')
    const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
    const userContainer = 'Users'

    let usersDeactivated = 0
    let b2cUsersDeleted = 0
    let usersToDeactivate = []

    if (subscription.subscriptionType === SubscriptionType.CLINIC) {
      if (subscription.contactEmail || subscription.billingDetails?.email) {
        const email = subscription.contactEmail || subscription.billingDetails?.email
        const users = await userRepository.getUserByEmail(email)

        if (users && users.length > 0) {
          const user = users.find(u => u.organizationId === subscription.organizationId && u.isActive)
          if (user) {
            usersToDeactivate.push(user)
            logging.logInfo(`Found clinic user to deactivate: ${user.email}`)
          }
        }
      } else {
        logging.logWarning(`Clinic subscription ${subscription.id} has no contact email, skipping user deactivation`)
      }
    } else {
      const orgUsers = await userRepository.getUsersByOrganizationId(subscription.organizationId)
      if (orgUsers && orgUsers.length > 0) {
        usersToDeactivate = orgUsers
        logging.logInfo(`Found ${orgUsers.length} organization user(s) to deactivate`)
      }
    }

    if (usersToDeactivate.length > 0) {
      for (const user of usersToDeactivate) {
        try {
          // Permanently delete B2C user (two-step: soft delete + permanent delete)
          // This bypasses the 30-day soft delete period and allows immediate user recreation
          if (user.b2cUserId) {
            try {
              await graphService.permanentlyDeleteUser(token.accessToken, user.b2cUserId)
              b2cUsersDeleted++
              logging.logInfo(`Permanently deleted B2C user: ${user.b2cUserId} (${user.email})`)
            } catch (b2cError) {
              logging.logError(`Failed to permanently delete B2C user ${user.b2cUserId}:`, b2cError)
            }
          }

          const updatedUser = {
            ...user,
            isActive: false,
            updated_by: 'system',
            updated_on: new Date().toISOString(),
          }

          await cosmosDbContext.upsertItem(user.id, updatedUser, userContainer)
          usersDeactivated++
          logging.logInfo(`Deactivated database user: ${user.id} (${user.email})`)
        } catch (userError) {
          logging.logError(`Error processing user ${user.id}:`, userError)
        }
      }
    } else {
      logging.logInfo(`No users found to deactivate for subscription ${subscription.id}`)
    }

    return { usersDeactivated, b2cUsersDeleted }
  }

  async processExpiredSubscriptions() {
    try {
      const graphService = require('./graph-service')
      const userRepository = require('../repositories/admin/user-repository')
      const { getExpiredSubscriptionsQuery } = require('../queries/subscription-query')
      const { SubscriptionType } = require('../common/constant')

      const query = getExpiredSubscriptionsQuery()
      const expiredSubscriptions = await cosmosDbContext.queryItems(query, subscriptionContainer)

      logging.logInfo(`Found ${expiredSubscriptions.length} expired subscription(s) to process`)

      let processedCount = 0
      let usersDeactivated = 0
      let b2cUsersDeleted = 0

      const token = await graphService.getToken()

      for (const subscription of expiredSubscriptions) {
        try {
          logging.logInfo(`Processing expired subscription: ${subscription.id} (type: ${subscription.subscriptionType}) for organization: ${subscription.organizationId}`)

          const result = await this.deactivateSubscriptionUsers(subscription, token)
          usersDeactivated += result.usersDeactivated
          b2cUsersDeleted += result.b2cUsersDeleted

          await subscriptionRepository.updateOrganizationSubscription(
            subscription.id,
            subscription.organizationId,
            { status: 'expired' }
          )
          processedCount++
          logging.logInfo(`Marked subscription ${subscription.id} as expired`)
        } catch (subError) {
          logging.logError(`Error processing subscription ${subscription.id}:`, subError)
        }
      }

      const message = `Processed ${processedCount} expired subscription(s): ${usersDeactivated} user(s) deactivated, ${b2cUsersDeleted} B2C user(s) deleted`
      logging.logInfo(message)

      return {
        success: true,
        message,
        data: {
          subscriptionsProcessed: processedCount,
          usersDeactivated,
          b2cUsersDeleted
        },
      }
    } catch (error) {
      logging.logError('Error in processExpiredSubscriptions:', error)
      return {
        success: false,
        message: error.message || 'Failed to process expired subscriptions',
      }
    }
  }

  // Subscriber Management (Combined Organization + Subscription)

  async createSubscriber(subscriberData, userId) {
    try {
      // Validate plan exists
      const planData = await subscriptionRepository.getPlanById(subscriberData.planId)
      if (!planData) {
        throw new Error('Subscription plan not found')
      }

      if (!planData.isActive) {
        throw new Error('Subscription plan is not active')
      }

      if (subscriberData.email) {
        const organizationRepository = require('../repositories/admin/organization-repository')
        const existingOrg = await organizationRepository.getOrganizationByEmail(subscriberData.email)

        if (existingOrg) {
          const existingSubscription = await subscriptionRepository.getActiveSubscriptionByOrganization(existingOrg.id)

          if (existingSubscription) {
            throw new Error('An active subscription already exists for this email address')
          }
        }
      }

      // Instantiate as SubscriptionPlan to get access to methods
      const plan = new SubscriptionPlan(planData)

      // Create organization
      const organizationId = uuidv4()

      let addressData
      if (typeof subscriberData.address === 'string') {
        addressData = {
          street: subscriberData.address,
          city: subscriberData.city || '',
          state: subscriberData.state || '',
          pincode: subscriberData.pincode || '',
          country: subscriberData.country || 'India',
        }
      } else if (typeof subscriberData.address === 'object' && subscriberData.address !== null) {
        addressData = {
          street: subscriberData.address.street || '',
          city: subscriberData.address.city || '',
          state: subscriberData.address.state || '',
          pincode: subscriberData.address.pincode || subscriberData.pincode || '',
          country: subscriberData.address.country || 'India',
        }
      } else {
        addressData = {
          street: subscriberData.addressStreet || '',
          city: subscriberData.city || '',
          state: subscriberData.state || '',
          pincode: subscriberData.pincode || '',
          country: subscriberData.country || 'India',
        }
      }

      const organizationModel = new OrganizationModel({
        id: organizationId,
        name: subscriberData.organizationName,
        contactEmail: subscriberData.email,
        contactPersonName: subscriberData.contactPerson,
        contactPhone: subscriberData.contactPhone || '',
        address: addressData,
        pan: subscriberData.pan || '',
        gstin: subscriberData.gstin || '',
        description: subscriberData.description || '',
        isActive: subscriberData.status === 'active',
        registrationFee: 0,
        created_by: userId,
        updated_by: userId,
      })

      // Validate organization model
      const orgValidation = organizationModel.validate()
      if (!orgValidation.isValid) {
        throw new Error(orgValidation.errors.join(', '))
      }

      // Create organization in database
      const organization = await organizationRepository.createOrganization(
        organizationModel.toJSON(),
      )

      // Determine billing type from subscription type or plan validity
      let billingType = subscriberData.billingType || 'yearly'
      if (subscriberData.subscriptionType) {
        billingType = subscriberData.subscriptionType.toLowerCase().includes('month')
          ? 'monthly'
          : 'yearly'
      }

      const isFreeTrial = !subscriberData.paymentId && subscriberData.status === 'pending'

      let endDate
      if (subscriberData.validity) {
        endDate = new Date(subscriberData.validity).toISOString()
      } else {
        const startDate = new Date()
        endDate = new Date(startDate)

        if (isFreeTrial) {
          endDate.setMonth(endDate.getMonth() + 1)
        } else {
          if (billingType === 'monthly') {
            endDate.setMonth(endDate.getMonth() + 1)
          } else {
            endDate.setFullYear(endDate.getFullYear() + 1)
          }
        }
        endDate = endDate.toISOString()
      }

      let totalAmount
      if (isFreeTrial) {
        totalAmount = 0
      } else {
        totalAmount = plan.calculateTotalAmount(billingType)
      }

      const cleanedBaseFeatures = this.cleanFeatures(plan.features)
      const cleanedAddOnFeatures = this.cleanFeatures(plan.addOnFeatures)

      // Create subscription
      const { SubscriptionType } = require('../common/constant')
      const subscriptionData = {
        organizationId: organization.id,
        planId: plan.id,
        planName: plan.planName,
        validity: plan.validity,
        billingType: billingType,
        features: cleanedBaseFeatures,
        addOnFeatures: cleanedAddOnFeatures,
        totalAmount: totalAmount,
        status: subscriberData.status || 'active',
        subscriptionType: SubscriptionType.ORGANIZATION,
        isFreeTrial: isFreeTrial,
        startDate: new Date().toISOString(),
        endDate: endDate,
        autoRenew: subscriberData.autoRenew !== undefined ? subscriberData.autoRenew : false,
        paymentMethod: 'razorpay',
        paymentId: subscriberData.paymentId || null,
        contactEmail: subscriberData.email || '', // Store contact email for user management
        created_by: userId,
      }

      const subscription = await subscriptionRepository.createOrganizationSubscription(
        subscriptionData,
      )

      let userCreationResult = null
      if (subscriberData.email) {
        try {
          const userRepository = require('../repositories/admin/user-repository')
          const existingUsers = await userRepository.getUserByEmail(subscriberData.email)
          const existingInactiveUser = existingUsers && existingUsers.length > 0
            ? existingUsers.find(u => !u.isActive)
            : null

          const graphService = require('./graph-service')
          let existingB2CUser = null
          try {
            const token = await graphService.getToken()
            existingB2CUser = await graphService.getUserByPrincipalName(
              token.accessToken,
              subscriberData.email
            )
          } catch (b2cCheckError) {
            logging.logInfo(`B2C user check for ${subscriberData.email}: User not found (expected for new/reactivated users)`)
          }

          if (existingB2CUser) {
            logging.logInfo(`B2C user already exists for email: ${subscriberData.email}. Re-enabling account with new password.`)

            if (existingInactiveUser) {
              try {
                const { generateSecurePassword } = require('../utils/password-utils')
                const temporaryPassword = generateSecurePassword()

                const token = await graphService.getToken()
                await graphService.updateUser(token.accessToken, existingB2CUser.id, {
                  accountEnabled: true,
                  passwordProfile: {
                    forceChangePasswordNextSignIn: true,
                    password: temporaryPassword
                  }
                })
                logging.logInfo(`Re-enabled B2C user with new password: ${existingB2CUser.id} (${subscriberData.email})`)

                const dbUser = {
                  ...existingInactiveUser,
                  b2cUserId: existingB2CUser.id,
                  isActive: true,
                  organizationId: organization.id,
                  userRole: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
                  userType: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
                  isOrganizationMainAdmin: true,
                  phoneNumber: subscriberData.contactPhone || existingInactiveUser.phoneNumber,
                  name: subscriberData.contactPerson || existingInactiveUser.name,
                  updated_by: 'system',
                  updated_on: new Date().toISOString(),
                }

                await cosmosDbContext.upsertItem(dbUser.id, dbUser, userContainer)
                logging.logInfo(`Reactivated existing database user for subscriber: ${subscriberData.email}`)

                await b2cService.sendWelcomeEmailWithB2CSetup(
                  subscriberData.email,
                  subscriberData.contactPerson || subscriberData.organizationName,
                  temporaryPassword,
                  true // isAdmin = true for organization subscriber
                )
                logging.logInfo(`Sent activation email to reactivated user: ${subscriberData.email}`)

                userCreationResult = { success: true, user: existingB2CUser, dbUser: dbUser, reactivated: true }
              } catch (reactivationError) {
                logging.logError('Error reactivating user account:', reactivationError)
                userCreationResult = { success: false, error: 'Failed to reactivate user account' }
              }
            } else {
              userCreationResult = {
                success: false,
                error: 'A user with this email already exists. Please contact support or use a different email.'
              }
            }
          } else {
            // B2C user doesn't exist, create new one
            // Auto-generate temporary password (same as user creation flow)
            const { generateSecurePassword } = require('../utils/password-utils')
            const temporaryPassword = generateSecurePassword()

            const tenantName = await secretManager.getSecret('TENANT_NAME')

            const b2cUser = {
              accountEnabled: true,
              displayName: subscriberData.contactPerson || subscriberData.organizationName,
              identities: [
                {
                  signInType: 'emailAddress',
                  issuer: `${tenantName}.onmicrosoft.com`,
                  issuerAssignedId: subscriberData.email,
                },
              ],
              passwordProfile: {
                forceChangePasswordNextSignIn: true,
                password: temporaryPassword, 
              },
              passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
            }

            const createdUser = await b2cService.createB2CUser(b2cUser)

          if (createdUser) {
            let dbUser

            if (existingInactiveUser) {
              
              dbUser = {
                ...existingInactiveUser,
                b2cUserId: createdUser.id, 
                isActive: true, // Reactivate
                organizationId: organization.id, 
                userRole: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
                userType: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
                isOrganizationMainAdmin: true,
                phoneNumber: subscriberData.contactPhone || existingInactiveUser.phoneNumber,
                name: subscriberData.contactPerson || existingInactiveUser.name,
                updated_by: 'system',
                updated_on: new Date().toISOString(),
              }

              try {
                await cosmosDbContext.upsertItem(dbUser.id, dbUser, userContainer)
                logging.logInfo(`Reactivated existing database user for subscriber: ${subscriberData.email}`)
              } catch (dbError) {
                logging.logError('Error reactivating database user for subscriber:', dbError)
              }
            } else {
              // Create new database user record with organization super admin role
              dbUser = {
                id: uuidv4(),
                name: subscriberData.contactPerson || subscriberData.organizationName,
                email: subscriberData.email,
                userRole: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
                userType: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
                organizationId: organization.id,
                b2cUserId: createdUser.id,
                isActive: true,
                isOrganizationMainAdmin: true, // Organization subscriber is the main admin
                phoneNumber: subscriberData.contactPhone || null,
                created_by: 'system',
                updated_by: 'system',
                created_on: new Date().toISOString(),
                updated_on: new Date().toISOString(),
              }

              try {
                await userService.addUser(dbUser)
                logging.logInfo(`Database user created for subscriber: ${subscriberData.email} with role: ${DefaultRoles.ORGANIZATION_SUPER_ADMIN}`)
              } catch (dbError) {
                logging.logError('Error creating database user for subscriber:', dbError)
                // Continue even if DB user creation fails - B2C user is already created
              }
            }

              await b2cService.sendWelcomeEmailWithB2CSetup(
                subscriberData.email,
                subscriberData.contactPerson || subscriberData.organizationName,
                temporaryPassword, 
                true // isAdmin = true for organization subscriber
              )
              userCreationResult = { success: true, user: createdUser, dbUser: dbUser }
            }
          }
        } catch (userError) {
          logging.logError('Error creating user for subscriber:', userError)

          userCreationResult = { success: false, error: userError.message }
        }
      }

      return {
        success: true,
        message: userCreationResult?.success
          ? 'Subscriber created successfully. Activation email sent.'
          : 'Subscriber created successfully.',
        data: {
          organization,
          subscription,
          userCreated: userCreationResult?.success || false,
        },
      }
    } catch (error) {
      logging.logError('Error in createSubscriber:', error)
      return {
        success: false,
        message: error.message || 'Failed to create subscriber',
      }
    }
  }

  async getAllSubscribers({ page = 1, limit, status, searchTerm, planId, subscriptionType }) {
    try {
      const { SUBSCRIPTION_ORGANIZATION_NAME, SubscriptionType } = require('../common/constant')

      let allSubscriptions = []

      if (status && planId) {
        allSubscriptions = await subscriptionRepository.getSubscriptionsByStatus(status)
        allSubscriptions = allSubscriptions.filter(sub => sub.planId === planId)
      } else if (status) {
        allSubscriptions = await subscriptionRepository.getSubscriptionsByStatus(status)
      } else if (planId) {
        allSubscriptions = await subscriptionRepository.getSubscriptionsByPlanId(planId)
      } else {
        const activeSubscriptions = await subscriptionRepository.getSubscriptionsByStatus('active')
        const pendingSubscriptions = await subscriptionRepository.getSubscriptionsByStatus('pending')
        const expiredSubscriptions = await subscriptionRepository.getSubscriptionsByStatus('expired')
        const cancelledSubscriptions = await subscriptionRepository.getSubscriptionsByStatus('cancelled')
        allSubscriptions = [
          ...activeSubscriptions,
          ...pendingSubscriptions,
          ...expiredSubscriptions,
          ...cancelledSubscriptions
        ]
      }

      if (subscriptionType) {
        allSubscriptions = allSubscriptions.filter(sub =>
          sub.subscriptionType === subscriptionType
        )
      }

      const subscriberPromises = allSubscriptions.map(async (subscription) => {
        const org = await organizationRepository.getOrganizationById(subscription.organizationId)
        if (!org) {
          logging.logError(`Organization not found for subscription ${subscription.id}, organizationId: ${subscription.organizationId}`)
          return null
        }

        let subscriberInfo
        if (subscription.subscriptionType === SubscriptionType.CLINIC) {
          subscriberInfo = {
            subscriberId: subscription.id, // Use subscription ID as subscriber ID for clinics
            subscriptionType: 'clinic',
            name: subscription.billingDetails?.contactName || subscription.contactEmail,
            email: subscription.contactEmail || subscription.billingDetails?.email,
            pincode: subscription.billingDetails?.pincode || '',
            organizationName: SUBSCRIPTION_ORGANIZATION_NAME,
            organizationId: org.id,
          }
        } else {
          subscriberInfo = {
            subscriberId: org.id, // Use organization ID as subscriber ID
            subscriptionType: 'organization',
            name: org.name,
            email: org.contactEmail,
            contactPersonName: org.contactPersonName,
            contactPhone: org.contactPhone,
            address: org.address,
            pincode: org.address?.pincode || '',
            pan: org.pan,
            gstin: org.gstin,
            organizationId: org.id,
          }
        }

        if (searchTerm && searchTerm.trim() !== '') {
          const searchLower = searchTerm.toLowerCase()
          const searchableText = `${subscriberInfo.name} ${subscriberInfo.email}`.toLowerCase()
          if (!searchableText.includes(searchLower)) {
            return null
          }
        }

        return {
          ...subscriberInfo,
          subscription,
          currentStatus: subscription.status,
        }
      })

      let subscribers = await Promise.all(subscriberPromises)
      subscribers = subscribers.filter(sub => sub !== null)

      subscribers.sort((a, b) => {
        const dateA = new Date(a.subscription.created_on || 0)
        const dateB = new Date(b.subscription.created_on || 0)
        return dateB - dateA
      })

      const totalCount = subscribers.length

      let paginatedSubscribers = subscribers
      let totalPages = 1

      if (limit) {
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        paginatedSubscribers = subscribers.slice(startIndex, endIndex)
        totalPages = Math.ceil(totalCount / limit)
      }

      return {
        success: true,
        data: paginatedSubscribers,
        total: totalCount,
        page,
        limit: limit || totalCount,
        totalPages,
      }
    } catch (error) {
      logging.logError('Error in getAllSubscribers:', error)
      return {
        success: false,
        message: error.message || 'Failed to get subscribers',
      }
    }
  }

  async getSubscriberById(subscriberId) {
    try {
      const { SUBSCRIPTION_ORGANIZATION_NAME, SubscriptionType } = require('../common/constant')

      let organization = await organizationRepository.getOrganizationById(subscriberId)

      if (organization) {
        const subscriptions = await subscriptionRepository.getOrganizationSubscriptionHistory(subscriberId)
        let activeSubscription = await subscriptionRepository.getActiveSubscriptionByOrganization(subscriberId)

        if (!activeSubscription && subscriptions && subscriptions.length > 0) {
          activeSubscription = subscriptions[0]
        }

        const responseData = {
          ...organization,
          contactPhone: organization.phoneNumber,
          activeSubscription,
          subscriptionHistory: subscriptions,
        }

        return {
          success: true,
          data: responseData,
        }
      }

      const subscription = await subscriptionRepository.getSubscriptionById(subscriberId)

      if (!subscription) {
        throw new Error('Subscriber not found')
      }

      if (subscription.subscriptionType !== SubscriptionType.CLINIC) {
        throw new Error('Subscriber not found')
      }

      organization = await organizationRepository.getOrganizationById(subscription.organizationId)

      if (!organization || organization.name !== SUBSCRIPTION_ORGANIZATION_NAME) {
        throw new Error('Subscriber not found')
      }

      const responseData = {
        subscriberId: subscription.id,
        subscriptionType: 'clinic',
        email: subscription.contactEmail,
        name: subscription.billingDetails?.contactName || subscription.contactEmail,
        phoneNumber: subscription.billingDetails?.phoneNumber || '',
        pincode: subscription.billingDetails?.pincode || '',
        organizationId: organization.id,
        organizationName: organization.name,
        activeSubscription: subscription,
        subscriptionHistory: [subscription],
      }

      return {
        success: true,
        data: responseData,
      }
    } catch (error) {
      logging.logError('Error in getSubscriberById:', error)
      return {
        success: false,
        message: error.message || 'Failed to get subscriber',
      }
    }
  }

  async updateSubscriber(subscriberId, updateData, userId) {
    try {
      const { SUBSCRIPTION_ORGANIZATION_NAME, SubscriptionType } = require('../common/constant')

      // Try to get as organization subscription first
      let existingOrg = await organizationRepository.getOrganizationById(subscriberId)

      if (!existingOrg) {
        // Try as clinic subscription (subscriberId is subscription ID)
        const subscription = await subscriptionRepository.getSubscriptionById(subscriberId)

        if (!subscription || subscription.subscriptionType !== SubscriptionType.CLINIC) {
          throw new Error('Subscriber not found')
        }

        // For clinic subscriptions, update the subscription's billingDetails and subscription fields
        const subscriptionUpdateData = {}

        // Update billing details
        if (updateData.name || updateData.phoneNumber || updateData.pincode) {
          subscriptionUpdateData.billingDetails = {
            ...(subscription.billingDetails || {}),
          }

          if (updateData.name) {
            subscriptionUpdateData.billingDetails.contactName = updateData.name
          }
          if (updateData.phoneNumber) {
            subscriptionUpdateData.billingDetails.phoneNumber = updateData.phoneNumber
          }
          if (updateData.pincode) {
            subscriptionUpdateData.billingDetails.pincode = updateData.pincode
          }
        }

        // Update subscription fields
        if (updateData.status) {
          subscriptionUpdateData.status = updateData.status
        }
        if (updateData.endDate) {
          subscriptionUpdateData.endDate = new Date(updateData.endDate).toISOString()
        }
        if (updateData.validity) {
          subscriptionUpdateData.endDate = new Date(updateData.validity).toISOString()
        }
        if (updateData.autoRenew !== undefined) {
          subscriptionUpdateData.autoRenew = updateData.autoRenew
        }
        if (updateData.billingType) {
          subscriptionUpdateData.billingType = updateData.billingType
        }

        // Update plan if provided
        if (updateData.planId) {
          const newPlanData = await subscriptionRepository.getPlanById(updateData.planId)
          if (!newPlanData) {
            throw new Error('Subscription plan not found')
          }

          const newPlan = new SubscriptionPlan(newPlanData)
          const billingType = subscriptionUpdateData.billingType || subscription.billingType || 'yearly'

          subscriptionUpdateData.planId = newPlan.id
          subscriptionUpdateData.planName = newPlan.planName
          subscriptionUpdateData.features = newPlan.features
          subscriptionUpdateData.addOnFeatures = newPlan.addOnFeatures
          subscriptionUpdateData.totalAmount = newPlan.calculateTotalAmount(billingType)
        }

        if (Object.keys(subscriptionUpdateData).length > 0) {
          subscriptionUpdateData.updated_by = userId
          subscriptionUpdateData.updated_on = new Date().toISOString()

          const updatedSubscription = await subscriptionRepository.updateOrganizationSubscription(
            subscription.id,
            subscription.organizationId,
            subscriptionUpdateData
          )

          // If status is being changed to 'expired' or 'cancelled', deactivate users
          if (updateData.status && (updateData.status === 'expired' || updateData.status === 'cancelled')) {
            logging.logInfo(`Clinic subscription status changed to '${updateData.status}'. Deactivating user...`)

            try {
              const graphService = require('./graph-service')
              const token = await graphService.getToken()
              const result = await this.deactivateSubscriptionUsers(updatedSubscription, token)
              logging.logInfo(`Manual clinic subscription ${updateData.status}: Deactivated ${result.usersDeactivated} user(s), permanently deleted ${result.b2cUsersDeleted} B2C user(s)`)
            } catch (deactivationError) {
              logging.logError('Error deactivating clinic user during manual subscription expiry:', deactivationError)
            }
          }

          return {
            success: true,
            message: 'Clinic subscriber updated successfully',
            data: {
              subscription: updatedSubscription,
            },
          }
        }

        return {
          success: true,
          message: 'No changes to update',
          data: {
            subscription: subscription,
          },
        }
      }

      // Organization subscription update logic (existing code)
      if (!existingOrg) {
        throw new Error('Subscriber not found')
      }

      // Update organization fields
      const updatedOrgData = {
        ...existingOrg,
        ...updateData,
        id: subscriberId,
        updated_by: userId,
        updatedAt: new Date().toISOString(),
      }

      // Handle address and pincode updates
      if (updateData.address || updateData.pincode) {
        if (typeof updateData.address === 'string') {
          updatedOrgData.address = {
            ...existingOrg.address,
            street: updateData.address,
            pincode: updateData.pincode || existingOrg.address?.pincode || '',
          }
        } else if (typeof updateData.address === 'object' && updateData.address !== null) {
          updatedOrgData.address = {
            ...existingOrg.address,
            ...updateData.address,
          }
          if (updateData.pincode) {
            updatedOrgData.address.pincode = updateData.pincode
          }
        } else if (updateData.pincode) {
          updatedOrgData.address = {
            ...existingOrg.address,
            pincode: updateData.pincode,
          }
        }
      }

      // Remove subscription-specific fields from organization update
      delete updatedOrgData.subscription
      delete updatedOrgData.subscriptionType
      delete updatedOrgData.validity
      delete updatedOrgData.planId
      delete updatedOrgData.planName
      delete updatedOrgData.pincode // Remove top-level pincode as it's in address
      delete updatedOrgData.contactPhone 
      delete updatedOrgData.billingType 
      delete updatedOrgData.autoRenew 
      delete updatedOrgData.endDate 
      delete updatedOrgData.status 
      delete updatedOrgData.description 

      // Update organization name if provided
      if (updateData.organizationName) {
        updatedOrgData.name = updateData.organizationName
      }

      // Update contact person if provided
      if (updateData.contactPerson) {
        updatedOrgData.contactPersonName = updateData.contactPerson
      }

      // Handle contactPhone mapping to phoneNumber
      if (updateData.contactPhone) {
        updatedOrgData.phoneNumber = updateData.contactPhone
      }

      // Validate updated organization
      const orgModel = new OrganizationModel(updatedOrgData)
      const validation = orgModel.validate()

      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const updatedOrg = await organizationRepository.updateOrganization(orgModel.toJSON())

      // Handle subscription updates if provided
      let updatedSubscription = null
      if (updateData.planId || updateData.status || updateData.validity || updateData.subscriptionType ||
          updateData.billingType || updateData.autoRenew || updateData.endDate) {
        let activeSubscription = await subscriptionRepository.getActiveSubscriptionByOrganization(
          subscriberId,
        )

        if (!activeSubscription) {
          const subscriptionHistory = await subscriptionRepository.getOrganizationSubscriptionHistory(subscriberId)
          if (subscriptionHistory && subscriptionHistory.length > 0) {
            activeSubscription = subscriptionHistory[0] // Most recent subscription
          }
        }

        if (activeSubscription) {
          const subscriptionUpdateData = {}

          if (updateData.status) {
            subscriptionUpdateData.status = updateData.status
          }

          if (updateData.validity) {
            subscriptionUpdateData.endDate = new Date(updateData.validity).toISOString()
          }

          if (updateData.endDate) {
            subscriptionUpdateData.endDate = new Date(updateData.endDate).toISOString()
          }

          if (updateData.autoRenew !== undefined) {
            subscriptionUpdateData.autoRenew = updateData.autoRenew
          }

          // Handle subscription type (billing type) change
          let newBillingType = activeSubscription.billingType
          if (updateData.billingType) {
            subscriptionUpdateData.billingType = updateData.billingType
            newBillingType = updateData.billingType
          } else if (updateData.subscriptionType) {
            newBillingType = updateData.subscriptionType.toLowerCase().includes('month')
              ? 'monthly'
              : 'yearly'
            subscriptionUpdateData.billingType = newBillingType
          }

          if (updateData.planId) {
            const newPlanData = await subscriptionRepository.getPlanById(updateData.planId)

            if (!newPlanData) {
              throw new Error('Subscription plan not found')
            }

            // Instantiate as SubscriptionPlan to get access to methods
            const newPlan = new SubscriptionPlan(newPlanData)

            subscriptionUpdateData.planId = newPlan.id
            subscriptionUpdateData.planName = newPlan.planName
            subscriptionUpdateData.features = newPlan.features
            subscriptionUpdateData.addOnFeatures = newPlan.addOnFeatures
            subscriptionUpdateData.totalAmount = newPlan.calculateTotalAmount(newBillingType)
          } else if (updateData.subscriptionType || updateData.billingType) {
            // If only billing type is changing (not the plan), recalculate amount
            const currentPlanData = await subscriptionRepository.getPlanById(
              activeSubscription.planId,
            )
            if (currentPlanData) {
              const currentPlan = new SubscriptionPlan(currentPlanData)
              subscriptionUpdateData.totalAmount = currentPlan.calculateTotalAmount(newBillingType)
            }
          }

          if (Object.keys(subscriptionUpdateData).length > 0) {
            updatedSubscription = await subscriptionRepository.updateOrganizationSubscription(
              activeSubscription.id,
              subscriberId,
              subscriptionUpdateData,
            )

            // If status is being changed to 'expired' or 'cancelled', deactivate users
            if (updateData.status && (updateData.status === 'expired' || updateData.status === 'cancelled')) {
              logging.logInfo(`Subscription status changed to '${updateData.status}' for subscription ${activeSubscription.id}. Deactivating users...`)

              try {
                const graphService = require('./graph-service')

                const token = await graphService.getToken()

                const result = await this.deactivateSubscriptionUsers(updatedSubscription, token)
                logging.logInfo(`Manual subscription ${updateData.status}: Deactivated ${result.usersDeactivated} user(s), permanently deleted ${result.b2cUsersDeleted} B2C user(s)`)
              } catch (deactivationError) {
                logging.logError('Error deactivating users during manual subscription expiry:', deactivationError)
                // Don't throw - subscription update was successful, just log the error
              }
            }
          }
        }
      }

      return {
        success: true,
        message: 'Subscriber updated successfully',
        data: {
          organization: updatedOrg,
          subscription: updatedSubscription,
        },
      }
    } catch (error) {
      logging.logError('Error in updateSubscriber:', error)
      return {
        success: false,
        message: error.message || 'Failed to update subscriber',
      }
    }
  }

  // Clinic Flow Methods

  async startClinicFreeTrial(clinicData) {
    try {
      const { email, name, phoneNumber, planId, billingType, roleId } = clinicData

      if (!email || !name || !planId) {
        throw new Error('Email, name, and plan ID are required')
      }

      const { SUBSCRIPTION_ORGANIZATION_NAME } = require('../common/constant')

      const organization = await organizationRepository.getOrganizationByName(SUBSCRIPTION_ORGANIZATION_NAME)
      if (!organization) {
        throw new Error(`Common subscription organization "${SUBSCRIPTION_ORGANIZATION_NAME}" not found. Please contact support.`)
      }

      const organizationId = organization.id

      const existingActiveSubscription = await subscriptionRepository.getActiveSubscriptionByEmailAndOrganization(
        email,
        organizationId
      )

      if (existingActiveSubscription) {
        return {
          success: false,
          message: 'You already have an active subscription. To change your plan, please upgrade or modify it from within the application.',
        }
      }

      const hasUsedTrial = await subscriptionRepository.hasUsedFreeTrial(email)
      if (hasUsedTrial) {
        return {
          success: false,
          message: 'Free trial has already been used for this email address',
        }
      }

      const plan = await subscriptionRepository.getPlanById(planId)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      if (!plan.isActive) {
        throw new Error('Subscription plan is not active')
      }

      // Create 30-day free trial subscription
      const startDate = new Date()
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + 30) // 30 days trial

      const cleanedBaseFeatures = this.cleanFeatures(plan.features)

      const { SubscriptionType } = require('../common/constant')
      const subscription = await subscriptionRepository.createOrganizationSubscription({
        organizationId: organizationId,
        planId: plan.id,
        planName: plan.planName,
        validity: plan.validity,
        billingType: billingType || 'yearly',
        features: cleanedBaseFeatures,
        addOnFeatures: { MRD: [], EMR: [], Billing: [] },
        status: 'pending',
        subscriptionType: SubscriptionType.CLINIC,
        isFreeTrial: true,
        trialUsed: true, // Mark trial as used for this email
        userCreated: true, // User created during trial
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        autoRenew: false,
        totalAmount: 0,
        paymentMethod: 'razorpay',
        paymentId: null,
        contactEmail: email,
        billingDetails: {
          contactName: name,
          email: email,
          pincode: '',
        },
        created_by: email,
      })

      try {
        const userRepository = require('../repositories/admin/user-repository')
        const existingUsers = await userRepository.getUserByEmail(email)
        const existingInactiveUser = existingUsers && existingUsers.length > 0
          ? existingUsers.find(u => !u.isActive)
          : null

        // Auto-generate temporary password
        const { generateSecurePassword } = require('../utils/password-utils')
        const temporaryPassword = generateSecurePassword()

        // Get tenant name from secret manager
        const tenantName = await secretManager.getSecret('TENANT_NAME')

        const b2cUser = {
          accountEnabled: true,
          displayName: name,
          identities: [
            {
              signInType: 'emailAddress',
              issuer: `${tenantName}.onmicrosoft.com`,
              issuerAssignedId: email,
            },
          ],
          passwordProfile: {
            forceChangePasswordNextSignIn: true,
            password: temporaryPassword, // Use auto-generated password
          },
          passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
        }

        const createdUser = await b2cService.createB2CUser(b2cUser)

        if (createdUser) {
          let roleName = DefaultRoles.DOCTOR // Default role
          if (roleId) {
            try {
              const role = await roleRepository.getRoleById(roleId)
              if (role && role.name) {
                roleName = role.name
              } else {
                logging.logError(`Role not found for roleId: ${roleId}, using default: ${DefaultRoles.DOCTOR}`)
              }
            } catch (roleError) {
              logging.logError('Error fetching role by ID:', roleError)
              // Continue with default role
            }
          }

          let dbUser

          if (existingInactiveUser) {
            
            dbUser = {
              ...existingInactiveUser,
              b2cUserId: createdUser.id, 
              isActive: true, // Reactivate
              organizationId: organizationId, 
              userRole: roleName,
              userType: roleName,
              phoneNumber: phoneNumber || existingInactiveUser.phoneNumber,
              name: name || existingInactiveUser.name,
              updated_by: email,
              updated_on: new Date().toISOString(),
            }

            try {
              await cosmosDbContext.upsertItem(dbUser.id, dbUser, userContainer)
              logging.logInfo(`Reactivated existing database user for clinic trial: ${email}`)
            } catch (dbError) {
              logging.logError('Error reactivating database user for clinic trial:', dbError)
            }
          } else {
            // Create new database user record with role from payload
            dbUser = {
              id: uuidv4(),
              name: name,
              email: email,
              userRole: roleName, // Role name from roleId lookup
              userType: roleName, // Same as userRole
              organizationId: organizationId,
              b2cUserId: createdUser.id,
              isActive: true,
              isOrganizationMainAdmin: false, // Clinic users are not organization admins
              phoneNumber: phoneNumber || null,
              created_by: email,
              updated_by: email,
              created_on: new Date().toISOString(),
              updated_on: new Date().toISOString(),
            }

            try {
              await userService.addUser(dbUser)
              logging.logInfo(`Database user created for clinic trial: ${email} with role: ${dbUser.userRole}`)
            } catch (dbError) {
              logging.logError('Error creating database user for clinic trial:', dbError)
              // Continue even if DB user creation fails - B2C user is already created
            }
          }

          await b2cService.sendWelcomeEmailWithB2CSetup(
            email,
            name,
            temporaryPassword, 
            false // isAdmin = false for clinic trial user
          )

          return {
            success: true,
            message: 'Free trial started successfully. Activation email sent. Trial valid for 30 days.',
            data: {
              organizationId,
              subscription,
              user: createdUser,
              dbUser: dbUser,
              trialEndDate: endDate.toISOString(),
            },
          }
        } else {
          return {
            success: false,
            message: 'Failed to create user account. Please contact support.',
          }
        }
      } catch (userError) {
        logging.logError('Error creating user for trial:', userError)

        if (userError.message && userError.message.includes('already exists')) {
          return {
            success: false,
            message: 'A user account already exists with this email. Please try subscribing directly or contact support.',
          }
        }

        return {
          success: false,
          message: `Failed to create user account: ${userError.message}`,
        }
      }
    } catch (error) {
      logging.logError('Error in startClinicFreeTrial:', error)
      return {
        success: false,
        message: error.message || 'Failed to start free trial',
      }
    }
  }

  async subscribeClinic(clinicData) {
    try {
      const {
        organizationId: providedOrgId,
        planId,
        billingType,
        selectedAddOnFeatures,
        paymentId,
        email,
        name,
        phoneNumber,
        roleId,
      } = clinicData

      if (!planId) {
        throw new Error('Plan ID is required')
      }

      const { SUBSCRIPTION_ORGANIZATION_NAME } = require('../common/constant')
      const organization = await organizationRepository.getOrganizationByName(SUBSCRIPTION_ORGANIZATION_NAME)
      if (!organization) {
        throw new Error(`Common subscription organization "${SUBSCRIPTION_ORGANIZATION_NAME}" not found. Please contact support.`)
      }

      if (providedOrgId && providedOrgId !== organization.id) {
        throw new Error('Invalid organization ID for clinic subscription')
      }

      const organizationId = organization.id

      const plan = await subscriptionRepository.getPlanById(planId)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      if (!plan.isActive) {
        throw new Error('Subscription plan is not active')
      }

      // Determine contact email and check for existing subscription
      let contactEmail = email
      let userCreated = false
      let createdUser = null
      let existingSubscription = null

      // Check if user already has an active or pending subscription
      if (email) {
        // getActiveSubscriptionByEmailAndOrganization returns both active and pending subscriptions
        existingSubscription = await subscriptionRepository.getActiveSubscriptionByEmailAndOrganization(
          email,
          organizationId
        )

        // If user has an active/pending subscription that's NOT a trial being upgraded, reject
        if (existingSubscription && !existingSubscription.isFreeTrial) {
          return {
            success: false,
            message: 'You already have an active subscription. To change your plan, please upgrade or modify it from within the application.',
          }
        }
      }

      // Check if B2C user already exists and if DB user is inactive (reactivation case)
      if (email && name) {
        const graphService = require('./graph-service')
        const userRepository = require('../repositories/admin/user-repository')
        const tenantName = await secretManager.getSecret('TENANT_NAME')

        const existingUsers = await userRepository.getUserByEmail(email)
        const existingInactiveUser = existingUsers && existingUsers.length > 0
          ? existingUsers.find(u => !u.isActive && u.organizationId === organizationId)
          : null

        try {
          const Token = await graphService.getToken()
          const existingB2CUser = await graphService.getUserByPrincipalName(
            Token.accessToken,
            email
          )

          if (existingB2CUser && existingInactiveUser) {
            // User exists and was deactivated - reactivate with new password
            logging.logInfo(`Reactivating B2C user for: ${email}`)

            const { generateSecurePassword } = require('../utils/password-utils')
            const temporaryPassword = generateSecurePassword()

            // Re-enable B2C account and reset password
            await graphService.updateUser(Token.accessToken, existingB2CUser.id, {
              accountEnabled: true,
              passwordProfile: {
                forceChangePasswordNextSignIn: true,
                password: temporaryPassword
              }
            })
            logging.logInfo(`Re-enabled B2C user with new password: ${existingB2CUser.id}`)

            await b2cService.sendWelcomeEmailWithB2CSetup(
              email,
              name,
              temporaryPassword,
              false // isAdmin = false for clinic user
            )
            logging.logInfo(`Sent activation email to reactivated clinic user: ${email}`)

            userCreated = true
            createdUser = existingB2CUser
          } else if (existingB2CUser) {
            logging.logInfo(`B2C user already exists and is active for: ${email}`)
            userCreated = true
            createdUser = existingB2CUser
          }
        } catch (b2cCheckError) {
          logging.logInfo(`B2C user does not exist for: ${email}, will create new user`)
        }
      }

      // If B2C user doesn't exist, create both B2C and database users
      if (!userCreated && email && name) {
        const userRepository = require('../repositories/admin/user-repository')
        const existingUsers = await userRepository.getUserByEmail(email)
        const existingInactiveUser = existingUsers && existingUsers.length > 0
          ? existingUsers.find(u => !u.isActive)
          : null

        // Create B2C user account
        try {
          const { generateSecurePassword } = require('../utils/password-utils')
          const temporaryPassword = generateSecurePassword()

          const tenantName = await secretManager.getSecret('TENANT_NAME')

          const b2cUser = {
            accountEnabled: true,
            displayName: name,
            identities: [
              {
                signInType: 'emailAddress',
                issuer: `${tenantName}.onmicrosoft.com`,
                issuerAssignedId: email,
              },
            ],
            passwordProfile: {
              forceChangePasswordNextSignIn: true,
              password: temporaryPassword,
            },
            passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
          }

          createdUser = await b2cService.createB2CUser(b2cUser)

          if (!createdUser) {
            throw new Error('Failed to create user account')
          }

          let roleName = DefaultRoles.DOCTOR // Default role
          if (roleId) {
            try {
              const role = await roleRepository.getRoleById(roleId)
              if (role && role.name) {
                roleName = role.name
              } else {
                logging.logError(`Role not found for roleId: ${roleId}, using default: ${DefaultRoles.DOCTOR}`)
              }
            } catch (roleError) {
              logging.logError('Error fetching role by ID:', roleError)
              // Continue with default role
            }
          }

          let dbUser

          if (existingInactiveUser) {
            dbUser = {
              ...existingInactiveUser,
              b2cUserId: createdUser.id, 
              isActive: true, // Reactivate
              organizationId: organizationId, 
              userRole: roleName,
              userType: roleName,
              phoneNumber: phoneNumber || existingInactiveUser.phoneNumber,
              name: name || existingInactiveUser.name,
              updated_by: email,
              updated_on: new Date().toISOString(),
            }

            try {
              await cosmosDbContext.upsertItem(dbUser.id, dbUser, userContainer)
              logging.logInfo(`Reactivated existing database user for clinic subscription: ${email}`)
            } catch (dbError) {
              logging.logError('Error reactivating database user for clinic subscription:', dbError)
            }
          } else {
            // Create new database user record with role from payload
            dbUser = {
              id: uuidv4(),
              name: name,
              email: email,
              userRole: roleName, 
              userType: roleName, 
              organizationId: organizationId,
              b2cUserId: createdUser.id,
              isActive: true,
              isOrganizationMainAdmin: false, 
              phoneNumber: phoneNumber || null,
              created_by: email,
              updated_by: email,
              created_on: new Date().toISOString(),
              updated_on: new Date().toISOString(),
            }

            try {
              await userService.addUser(dbUser)
              logging.logInfo(`Database user created for clinic subscription: ${email} with role: ${dbUser.userRole}`)
            } catch (dbError) {
              logging.logError('Error creating database user for clinic subscription:', dbError)
              // Continue even if DB user creation fails - B2C user is already created
            }
          }

          await b2cService.sendWelcomeEmailWithB2CSetup(
            email,
            name,
            temporaryPassword,
            false // isAdmin = false for clinic user
          )

          userCreated = true
        } catch (userError) {
          logging.logError('Error creating user for direct subscription:', userError)

          // Check if user already exists (conflict error)
          if (userError.message && userError.message.includes('already exists')) {
            // User already exists, mark as created and continue
            logging.logInfo(`User already exists for email: ${email}, continuing with subscription creation`)
            userCreated = true
          } else {
            // Other error, fail the operation
            throw new Error(`Failed to create user account: ${userError.message}`)
          }
        }
      }

      const billingTypeToUse = billingType || 'yearly'
      const startDate = new Date()
      const endDate = new Date(startDate)

      if (billingTypeToUse === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1)
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1)
      }

      let totalAmount = 0
      Object.values(plan.features || {}).forEach(featureArray => {
        featureArray.forEach(feature => {
          totalAmount += billingTypeToUse === 'monthly'
            ? (feature.monthlyAmount || 0)
            : (feature.yearlyAmount || 0)
        })
      })

      let selectedAddOns = selectedAddOnFeatures || {}
      if (selectedAddOns && Object.keys(selectedAddOns).length > 0) {
        Object.values(selectedAddOns).forEach(featureArray => {
          if (Array.isArray(featureArray)) {
            featureArray.forEach(feature => {
              totalAmount += billingTypeToUse === 'monthly'
                ? (feature.monthlyAmount || 0)
                : (feature.yearlyAmount || 0)
            })
          }
        })
      }

      const cleanedBaseFeatures = this.cleanFeatures(plan.features)
      const cleanedAddOnFeatures = this.cleanFeatures(selectedAddOns)

      if (existingSubscription && existingSubscription.isFreeTrial) {
        await subscriptionRepository.updateOrganizationSubscription(
          existingSubscription.id,
          organizationId,
          { status: 'expired' }
        )
      }

      const { SubscriptionType } = require('../common/constant')
      const subscription = await subscriptionRepository.createOrganizationSubscription({
        organizationId: organizationId,
        planId: plan.id,
        planName: plan.planName,
        validity: plan.validity,
        billingType: billingTypeToUse,
        features: cleanedBaseFeatures,
        addOnFeatures: cleanedAddOnFeatures,
        status: paymentId ? 'active' : 'pending',
        subscriptionType: SubscriptionType.CLINIC,
        isFreeTrial: false,
        trialUsed: existingSubscription ? true : false, // Mark trial used only if came from trial
        userCreated: userCreated, // Use the dynamic flag based on whether user was created
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        autoRenew: false,
        totalAmount,
        paymentMethod: 'razorpay',
        paymentId: paymentId || null,
        contactEmail: contactEmail,
        billingDetails: {
          contactName: name || organization.contactPersonName || organization.name,
          email: contactEmail,
          pincode: '',
        },
        created_by: contactEmail,
      })

      // Build response message based on whether this was upgrade or new subscription
      const message = existingSubscription
        ? 'Subscription upgraded successfully.'
        : 'Subscription created successfully. Welcome email sent.'

      const responseData = {
        subscription,
        organizationId,
      }

      // Include user info if user was just created
      if (createdUser) {
        responseData.user = createdUser
      }

      return {
        success: true,
        message: message,
        data: responseData,
      }
    } catch (error) {
      logging.logError('Error in subscribeClinic:', error)
      return {
        success: false,
        message: error.message || 'Failed to subscribe clinic',
      }
    }
  }

  /**
   * Change clinic subscription plan
   * Used when an existing clinic user wants to upgrade/downgrade to a different plan
   * Requires payment for the new plan
   */
  async changeClinicPlan(changePlanData) {
    try {
      const {
        email,
        newPlanId,
        billingType,
        selectedAddOnFeatures,
        paymentId,
      } = changePlanData

      if (!email) {
        throw new Error('Email is required to identify the clinic')
      }

      if (!newPlanId) {
        throw new Error('New plan ID is required')
      }

      if (!paymentId) {
        throw new Error('Payment ID is required for plan change')
      }

      const { SUBSCRIPTION_ORGANIZATION_NAME } = require('../common/constant')
      const organization = await organizationRepository.getOrganizationByName(SUBSCRIPTION_ORGANIZATION_NAME)

      if (!organization) {
        throw new Error(`Common subscription organization "${SUBSCRIPTION_ORGANIZATION_NAME}" not found. Please contact support.`)
      }

      const organizationId = organization.id

      // Find the current active subscription for this clinic email
      const currentSubscription = await subscriptionRepository.getActiveSubscriptionByEmailAndOrganization(
        email,
        organizationId
      )

      if (!currentSubscription) {
        throw new Error('No active subscription found for this clinic. Please subscribe first.')
      }

      // Validate new plan
      const newPlan = await subscriptionRepository.getPlanById(newPlanId)
      if (!newPlan) {
        throw new Error('New subscription plan not found')
      }

      if (!newPlan.isActive) {
        throw new Error('New subscription plan is not active')
      }

      // Calculate billing for new plan
      const billingTypeToUse = billingType || currentSubscription.billingType || 'yearly'
      const startDate = new Date()
      const endDate = new Date(startDate)

      if (billingTypeToUse === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1)
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1)
      }

      let totalAmount = 0
      Object.values(newPlan.features || {}).forEach(featureArray => {
        featureArray.forEach(feature => {
          totalAmount += billingTypeToUse === 'monthly'
            ? (feature.monthlyAmount || 0)
            : (feature.yearlyAmount || 0)
        })
      })

      let selectedAddOns = selectedAddOnFeatures || {}
      if (selectedAddOns && Object.keys(selectedAddOns).length > 0) {
        Object.values(selectedAddOns).forEach(featureArray => {
          if (Array.isArray(featureArray)) {
            featureArray.forEach(feature => {
              totalAmount += billingTypeToUse === 'monthly'
                ? (feature.monthlyAmount || 0)
                : (feature.yearlyAmount || 0)
            })
          }
        })
      }

      const cleanedBaseFeatures = this.cleanFeatures(newPlan.features)
      const cleanedAddOnFeatures = this.cleanFeatures(selectedAddOns)

      // Mark current subscription as expired
      await subscriptionRepository.updateOrganizationSubscription(
        currentSubscription.id,
        organizationId,
        { status: 'expired', updated_by: email }
      )

      // Create new subscription with new plan
      const { SubscriptionType } = require('../common/constant')
      const newSubscription = await subscriptionRepository.createOrganizationSubscription({
        organizationId: organizationId,
        planId: newPlan.id,
        planName: newPlan.planName,
        validity: newPlan.validity,
        billingType: billingTypeToUse,
        features: cleanedBaseFeatures,
        addOnFeatures: cleanedAddOnFeatures,
        status: 'active',
        subscriptionType: SubscriptionType.CLINIC,
        isFreeTrial: false,
        trialUsed: currentSubscription.trialUsed || false,
        userCreated: true, // User already exists
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        autoRenew: false,
        totalAmount,
        paymentMethod: 'razorpay',
        paymentId: paymentId,
        contactEmail: email,
        billingDetails: {
          contactName: currentSubscription.billingDetails?.contactName || currentSubscription.contactEmail,
          email: email,
          pincode: currentSubscription.billingDetails?.pincode || '',
        },
        created_by: email,
      })

      // Send plan change confirmation email
      try {
        await emailService.sendEmail({
          to: email,
          subject: 'Plan Change Confirmation - ArcaAI EMR',
          html: `
            <h2>Plan Change Successful</h2>
            <p>Your subscription plan has been successfully changed.</p>
            <p><strong>Previous Plan:</strong> ${currentSubscription.planName}</p>
            <p><strong>New Plan:</strong> ${newPlan.planName}</p>
            <p><strong>Billing Type:</strong> ${billingTypeToUse}</p>
            <p><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</p>
            <p>Thank you for choosing ArcaAI EMR!</p>
          `
        })
      } catch (emailError) {
        logging.logError('Error sending plan change email:', emailError)
        // Don't fail the operation if email fails
      }

      return {
        success: true,
        message: 'Plan changed successfully.',
        data: {
          previousSubscription: {
            id: currentSubscription.id,
            planName: currentSubscription.planName,
            status: 'expired',
          },
          newSubscription: newSubscription,
          organizationId,
        },
      }
    } catch (error) {
      logging.logError('Error in changeClinicPlan:', error)
      return {
        success: false,
        message: error.message || 'Failed to change clinic plan',
      }
    }
  }
}

module.exports = new SubscriptionService()
