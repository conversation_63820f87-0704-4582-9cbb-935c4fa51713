const subscriptionService = require('../services/subscription-service')
const paymentService = require('../services/payment-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const logging = require('../common/logging')
const { PaymentType } = require('../common/constant')

class SubscriptionHandler {
  // Subscription Plan Handlers

  async createPlan(req, decode) {
    try {
      const planData = await req.json()

      if (!planData.planName || !planData.validity) {
        return jsonResponse(
          'Plan name and validity are required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.createSubscriptionPlan(
        planData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in createPlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async replacePlan(req, decode) {
    try {
      const planId = req.query.get('id')

      if (!planId) {
        return jsonResponse('Plan ID is required', HttpStatusCode.BadRequest)
      }

      const planData = await req.json()

      const result = await subscriptionService.replaceSubscriptionPlan(
        planId,
        planData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in replacePlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updatePlan(req, decode) {
    try {
      const planId = req.query.get('id')

      if (!planId) {
        return jsonResponse('Plan ID is required', HttpStatusCode.BadRequest)
      }

      const planData = await req.json()

      const result = await subscriptionService.updateSubscriptionPlan(
        planId,
        planData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in updatePlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deletePlan(req, decode) {
    try {
      const planId = req.query.get('id')

      if (!planId) {
        return jsonResponse('Plan ID is required', HttpStatusCode.BadRequest)
      }

      const result = await subscriptionService.deleteSubscriptionPlan(planId)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.message, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in deletePlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deactivatePlan(req, decode) {
    try {
      const planId = req.query.get('id')

      if (!planId) {
        return jsonResponse('Plan ID is required', HttpStatusCode.BadRequest)
      }

      const result = await subscriptionService.deactivateSubscriptionPlan(
        planId,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in deactivatePlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPlan(req, decode) {
    try {
      const planId = req.query.get('id')

      if (!planId) {
        return jsonResponse('Plan ID is required', HttpStatusCode.BadRequest)
      }

      const result = await subscriptionService.getSubscriptionPlan(planId)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.NotFound)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getAllPlans(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')

      const result = await subscriptionService.getAllSubscriptionPlans(
        organizationId,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          plans: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in getAllPlans handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchPlans(req, decode) {
    try {
      const planName = req.query.get('planName')
      const validity = req.query.get('validity')

      const searchParams = {}
      if (planName) searchParams.planName = planName
      if (validity) searchParams.validity = validity

      const result = await subscriptionService.searchSubscriptionPlans(
        searchParams,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          plans: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in searchPlans handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Organization Plan Handlers (special plan for organization-level settings)

  async createOrganizationPlan(req, decode) {
    try {
      const planData = await req.json()

      const result = await subscriptionService.createOrganizationPlan(
        planData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in createOrganizationPlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getOrganizationPlan(req, decode) {
    try {
      const result = await subscriptionService.getOrganizationPlan()

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.NotFound)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getOrganizationPlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateOrganizationPlan(req, decode) {
    try {
      const planData = await req.json()

      if (!planData.planName && !planData.description) {
        return jsonResponse(
          'At least one of planName or description is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.updateOrganizationPlan(
        planData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in updateOrganizationPlan handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Organization Subscription Handlers

  async subscribeOrganization(req, decode) {
    try {
      const subscriptionData = await req.json()

      if (!subscriptionData.organizationId || !subscriptionData.planId) {
        return jsonResponse(
          'Organization ID and Plan ID are required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.subscribeOrganization(
        subscriptionData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in subscribeOrganization handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateOrganizationSubscription(req, decode) {
    try {
      const subscriptionId = req.query.get('id')
      const organizationId = req.query.get('organizationId')

      if (!subscriptionId || !organizationId) {
        return jsonResponse(
          'Subscription ID and Organization ID are required',
          HttpStatusCode.BadRequest,
        )
      }

      const updateData = await req.json()

      const result = await subscriptionService.updateOrganizationSubscription(
        subscriptionId,
        organizationId,
        updateData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError(
        'Error in updateOrganizationSubscription handler:',
        error,
      )
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getOrganizationSubscription(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse(
          'Organization ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result =
        await subscriptionService.getOrganizationActiveSubscription(
          organizationId,
        )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.NotFound)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getOrganizationSubscription handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getOrganizationSubscriptionHistory(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse(
          'Organization ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result =
        await subscriptionService.getOrganizationSubscriptionHistory(
          organizationId,
        )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          subscriptions: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError(
        'Error in getOrganizationSubscriptionHistory handler:',
        error,
      )
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async cancelSubscription(req, decode) {
    try {
      const subscriptionId = req.query.get('id')
      const organizationId = req.query.get('organizationId')

      if (!subscriptionId || !organizationId) {
        return jsonResponse(
          'Subscription ID and Organization ID are required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.cancelOrganizationSubscription(
        subscriptionId,
        organizationId,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in cancelSubscription handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async renewSubscription(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse(
          'Organization ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.renewOrganizationSubscription(
        organizationId,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in renewSubscription handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async checkFeatureAccess(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')
      const featureName = req.query.get('featureName')

      if (!organizationId || !featureName) {
        return jsonResponse(
          'Organization ID and Feature Name are required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.checkFeatureAccess(
        organizationId,
        featureName,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          hasAccess: result.hasAccess,
          feature: result.feature,
          message: result.message,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in checkFeatureAccess handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getUpcomingRenewals(req, decode) {
    try {
      const daysAhead = req.query.get('daysAhead')
      const organizationId = req.query.get('organizationId')

      const result = await subscriptionService.getUpcomingRenewals(
        daysAhead ? parseInt(daysAhead) : 30,
        organizationId,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          renewals: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in getUpcomingRenewals handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getSubscriptionAnalytics(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')

      const result = await subscriptionService.getSubscriptionAnalytics(
        organizationId,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getSubscriptionAnalytics handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async processExpiredSubscriptions(req, decode) {
    try {
      const result = await subscriptionService.processExpiredSubscriptions()

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in processExpiredSubscriptions handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Subscriber Management (Combined Organization + Subscription)

  async createSubscriber(req, decode) {
    try {
      const subscriberData = await req.json()

      // Validate required fields
      if (
        !subscriberData.organizationName ||
        !subscriberData.email ||
        !subscriberData.contactPerson
      ) {
        return jsonResponse(
          'Organization name, email, and contact person are required',
          HttpStatusCode.BadRequest,
        )
      }

      if (!subscriberData.planId) {
        return jsonResponse('Plan ID is required', HttpStatusCode.BadRequest)
      }

      const result = await subscriptionService.createSubscriber(
        subscriberData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in createSubscriber handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getAllSubscribers(req, decode) {
    try {
      const page = parseInt(req.query.get('page')) || 1
      const limit = req.query.get('limit')
        ? parseInt(req.query.get('limit'))
        : undefined
      const status = req.query.get('status') // active, inactive, expired
      const searchTerm = req.query.get('search')
      const planId = req.query.get('planId')
      const subscriptionType = req.query.get('subscriptionType') // organization, clinic

      const result = await subscriptionService.getAllSubscribers({
        page,
        limit,
        status,
        searchTerm,
        planId,
        subscriptionType,
      })

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          subscribers: result.data,
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in getAllSubscribers handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getSubscriberById(req, decode) {
    try {
      const subscriberId = req.query.get('id')

      if (!subscriberId) {
        return jsonResponse(
          'Subscriber ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.getSubscriberById(subscriberId)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.NotFound)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getSubscriberById handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateSubscriber(req, decode) {
    try {
      const subscriberId = req.query.get('id')

      if (!subscriberId) {
        return jsonResponse(
          'Subscriber ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const updateData = await req.json()

      const result = await subscriptionService.updateSubscriber(
        subscriberId,
        updateData,
        decode.oid,
      )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in updateSubscriber handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Pre-Payment Validation Handler

  async validateEmailForSubscription(req, decode) {
    try {
      const body = await req.json()
      const { email } = body

      if (!email) {
        return jsonResponse(
          'Email is required for validation',
          HttpStatusCode.BadRequest,
        )
      }

      const graphService = require('../services/graph-service')
      let b2cUserExists = false

      try {
        const token = await graphService.getToken()
        const existingB2CUser = await graphService.getUserByPrincipalName(
          token.accessToken,
          email,
        )

        if (existingB2CUser && existingB2CUser.accountEnabled) {
          return jsonResponse(
            {
              isValid: false,
              canProceed: false,
              message:
                'You already have an active subscription.\nTo change your plan, please upgrade or modify it from within the application.',
              existingUser: {
                email: email,
                displayName: existingB2CUser.displayName,
              },
            },
            HttpStatusCode.Ok,
          )
        } else if (existingB2CUser && !existingB2CUser.accountEnabled) {
          b2cUserExists = true
        }
      } catch (b2cError) {
        if (b2cError.message && b2cError.message.includes('404')) {
          logging.logInfo(
            `B2C user check for ${email}: User not found - checking database`,
          )
          b2cUserExists = false
        } else {
          throw b2cError
        }
      }

      if (!b2cUserExists) {
        const subscriptionRepository = require('../repositories/subscription-repository')
        const organizationRepository = require('../repositories/admin/organization-repository')
        const { SUBSCRIPTION_ORGANIZATION_NAME } = require('../common/constant')

        const clinicOrg = await organizationRepository.getOrganizationByName(
          SUBSCRIPTION_ORGANIZATION_NAME,
        )
        if (clinicOrg) {
          const clinicSubscription =
            await subscriptionRepository.getActiveSubscriptionByEmailAndOrganization(
              email,
              clinicOrg.id,
            )

          if (clinicSubscription) {
            return jsonResponse(
              {
                isValid: false,
                canProceed: false,
                message:
                  'You already have an active subscription.\nTo change your plan, please upgrade or modify it from within the application.',
                existingSubscription: {
                  id: clinicSubscription.id,
                  status: clinicSubscription.status,
                  planName: clinicSubscription.planName,
                },
              },
              HttpStatusCode.Ok,
            )
          }
        }

        const existingOrg = await organizationRepository.getOrganizationByEmail(
          email,
        )
        if (existingOrg) {
          const orgSubscription =
            await subscriptionRepository.getActiveSubscriptionByOrganization(
              existingOrg.id,
            )

          if (orgSubscription) {
            return jsonResponse(
              {
                isValid: false,
                canProceed: false,
                message:
                  'You already have an active subscription.\nTo change your plan, please upgrade or modify it from within the application.',
                existingSubscription: {
                  id: orgSubscription.id,
                  status: orgSubscription.status,
                  planName: orgSubscription.planName,
                  organizationName: existingOrg.name,
                },
              },
              HttpStatusCode.Ok,
            )
          }
        }
      }

      return jsonResponse(
        {
          isValid: true,
          canProceed: true,
          message: 'Email is available for subscription',
          existingUser: null,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in validateEmailForSubscription handler:', error)
      return jsonResponse(
        'Failed to validate email for subscription',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Subscription Payment Handlers

  async createSubscriptionPaymentOrder(req, decode) {
    try {
      const paymentData = await req.json()

      const requiredFields = ['amount', 'organizationId', 'planId']
      const missingFields = requiredFields.filter(
        (field) => !paymentData[field],
      )

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      // If subscriberEmail is not provided, fetch it from organization
      if (!paymentData.subscriberEmail) {
        const organizationRepository = require('../repositories/organization-repository')
        const organization = await organizationRepository.getOrganizationById(
          paymentData.organizationId,
        )

        if (!organization) {
          return jsonResponse('Organization not found', HttpStatusCode.NotFound)
        }

        const { SUBSCRIPTION_ORGANIZATION_NAME } = require('../common/constant')

        // Check if this is the common clinic organization
        if (organization.name === SUBSCRIPTION_ORGANIZATION_NAME) {
          // For clinic subscriptions, subscriberEmail is required in payload
          return jsonResponse(
            'subscriberEmail is required for clinic subscription payments',
            HttpStatusCode.BadRequest,
          )
        }

        // For regular organization subscriptions, use organization's contact email
        paymentData.subscriberEmail = organization.contactEmail
      }

      // Convert amount to paisa (Razorpay expects amount in smallest currency unit)
      paymentData.amount = Math.round(paymentData.amount * 100)
      paymentData.paymentType = PaymentType.SUBSCRIPTION || 'subscription'
      paymentData.patientId = paymentData.patientId || '' // Optional: keep for backward compatibility
      paymentData.description =
        paymentData.description ||
        `Subscription payment for ${paymentData.planName || 'plan'}`

      const result = await paymentService.createOrder(paymentData)

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError(
        'Error in createSubscriptionPaymentOrder handler:',
        error,
      )
      return jsonResponse(
        'Failed to create subscription payment order',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async verifySubscriptionPayment(req, decode) {
    try {
      const verificationData = await req.json()

      const requiredFields = [
        'razorpay_order_id',
        'razorpay_payment_id',
        'razorpay_signature',
      ]
      const missingFields = requiredFields.filter(
        (field) => !verificationData[field],
      )

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      const result = await paymentService.verifyPayment(verificationData)

      if (result.verified) {
        return jsonResponse(result, HttpStatusCode.Ok)
      } else {
        return jsonResponse(result, HttpStatusCode.BadRequest)
      }
    } catch (error) {
      logging.logError('Error in verifySubscriptionPayment handler:', error)
      return jsonResponse(
        'Subscription payment verification failed',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Clinic Flow Handlers

  async startClinicTrial(req) {
    try {
      const body = await req.json()

      const result = await subscriptionService.startClinicFreeTrial(body)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in startClinicTrial handler:', error)
      return jsonResponse(
        error.message || 'Failed to start trial',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async subscribeClinic(req) {
    try {
      const body = await req.json()

      const result = await subscriptionService.subscribeClinic(body)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in subscribeClinic handler:', error)
      return jsonResponse(
        error.message || 'Failed to subscribe clinic',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async changeClinicPlan(req) {
    try {
      const body = await req.json()

      const result = await subscriptionService.changeClinicPlan(body)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in changeClinicPlan handler:', error)
      return jsonResponse(
        error.message || 'Failed to change clinic plan',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get features under a subscription
  async getSubscriptionFeatures(req, decode) {
    try {
      const subscriptionId = req.query.get('subscriptionId')
      const subscriberId = req.query.get('subscriberId')

      if (!subscriptionId && !subscriberId) {
        return jsonResponse(
          'Either subscriptionId or subscriberId is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await subscriptionService.getSubscriptionFeatures({
        subscriptionId,
        subscriberId,
      })

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getSubscriptionFeatures handler:', error)
      return jsonResponse(
        error.message || 'Failed to get subscription features',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Get features under organization subscription for UI feature enabling/disabling
  async getOrganizationSubscriptionFeatures(req, decode) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse(
          'organizationId is required',
          HttpStatusCode.BadRequest,
        )
      }

      const result =
        await subscriptionService.getOrganizationSubscriptionFeatures(
          organizationId,
        )

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError(
        'Error in getOrganizationSubscriptionFeatures handler:',
        error,
      )
      return jsonResponse(
        error.message || 'Failed to get organization subscription features',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new SubscriptionHandler()
