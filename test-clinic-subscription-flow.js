/**
 * Practical test script for clinic subscription flow
 * Uses actual feature IDs from the database
 */

// Test subscription plan using actual feature IDs from your features-list
const testSubscriptionPlan = {
  planName: "Clinic Pro Test Plan",
  description: "Test plan for clinic subscription with real features",
  validity: "Both",
  features: {
    MRD: [
      // MRD Dashboard
      { featureId: "0a3ffbcb-0a79-4ffa-8384-f2527424778b", monthlyAmount: 500, yearlyAmount: 5000 },
      // Book Consultation
      { featureId: "51b59eb5-55dc-43b5-a181-3ff5528671a5", monthlyAmount: 300, yearlyAmount: 3000 }
    ],
    EMR: [
      // EMR Dashboard
      { featureId: "f266f098-b95e-463a-a9f9-e611f8c32655", monthlyAmount: 800, yearlyAmount: 8000 },
      // Consultation
      { featureId: "d791b859-7d4a-4f7d-acfb-dc66006febee", monthlyAmount: 1000, yearlyAmount: 10000 }
    ],
    Billing: [
      // Appointment Booking Payment
      { featureId: "c6538d65-49f4-4747-8f56-919b780d08dc", monthlyAmount: 200, yearlyAmount: 2000 }
    ]
  },
  addOnFeatures: {
    MRD: [],
    EMR: [
      // Consultation-Ambient Listening
      { featureId: "9eff5791-b160-45e1-983a-21055872c33e", monthlyAmount: 600, yearlyAmount: 6000 },
      // Consultation-Doc Assist
      { featureId: "707ed9d9-7773-4989-8e8b-d597efb16b0b", monthlyAmount: 400, yearlyAmount: 4000 },
      // OCR
      { featureId: "5210d433-e9a6-4d9b-95e3-3dd3dcdb7fa9", monthlyAmount: 500, yearlyAmount: 5000 }
    ],
    Billing: []
  },
  isActive: true
}

// Test clinic subscription data
const testClinicSubscription = {
  email: "<EMAIL>",
  name: "Dr. Test Clinic",
  phoneNumber: "+1234567890",
  planId: "REPLACE_WITH_PLAN_ID_FROM_STEP_1",
  billingType: "yearly",
  selectedAddOnFeatures: {
    EMR: [
      // Select Ambient Listening and OCR as add-ons
      { featureId: "9eff5791-b160-45e1-983a-21055872c33e", monthlyAmount: 600, yearlyAmount: 6000 },
      { featureId: "5210d433-e9a6-4d9b-95e3-3dd3dcdb7fa9", monthlyAmount: 500, yearlyAmount: 5000 }
    ]
  },
  paymentId: "test-payment-id-12345"
}

console.log('=== CLINIC SUBSCRIPTION TESTING SCRIPT ===')
console.log('Using actual feature IDs from your database\n')

console.log('📋 EXPECTED PERMISSIONS FOR TEST CLINIC:')
console.log('Base Features:')
console.log('  - mrd.dashboard.view (MRD Dashboard)')
console.log('  - mrd.consultation.book (Book Consultation)')
console.log('  - emr.dashboard.view (EMR Dashboard)')
console.log('  - emr.consultation.manage, emr.consultation.view, emr.consultation.create, emr.consultation.edit (Consultation)')
console.log('  - emr.payment.appointment_booking (Appointment Booking Payment)')
console.log('')
console.log('Add-on Features:')
console.log('  - emr.consultation.ambient-listening (Ambient Listening)')
console.log('  - emr.consultation.doc-assist (Doc Assist)')
console.log('  - emr.lab-master.ocr (OCR)')
console.log('')

console.log('🚀 TESTING STEPS:')
console.log('')
console.log('STEP 1: Create Subscription Plan')
console.log('POST /api/v0.1/subscription/plans')
console.log('Body:', JSON.stringify(testSubscriptionPlan, null, 2))
console.log('')

console.log('STEP 2: Test Clinic Subscription (Replace planId with result from Step 1)')
console.log('POST /api/v0.1/clinic/subscription?action=subscribe')
console.log('Body:', JSON.stringify(testClinicSubscription, null, 2))
console.log('')

console.log('STEP 3: Verify User API')
console.log('GET /api/v0.1/user?email=<EMAIL>')
console.log('Expected: userType="clinic", subscriberId present, permissionKeys array with 8+ permissions')
console.log('')

console.log('STEP 4: Test Organization Features API')
console.log('GET /api/v0.1/organization/subscription/features?organizationId={organizationId}')
console.log('Expected: Features organized by type/subType with isEnabled flags')
console.log('')

console.log('STEP 5: Test Permission Validation')
console.log('Try accessing APIs that require the permissions listed above')
console.log('Should work for enabled features, fail for disabled ones')

console.log('\n✅ IMPLEMENTATION COMPLETE - READY FOR TESTING!')

module.exports = {
  testSubscriptionPlan,
  testClinicSubscription
}
