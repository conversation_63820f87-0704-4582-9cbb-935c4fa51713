/**
 * Test script to debug user API permission issue
 */

const userHandler = require('./src/handlers/user-handler')

async function testUserAPI() {
  try {
    console.log('🧪 Testing User API for clinic user...')
    console.log('')
    
    const testEmail = '<EMAIL>'
    console.log(`📧 Testing email: ${testEmail}`)
    console.log('')
    
    console.log('🔍 Calling getUserDetailsByEmail...')
    const result = await userHandler.getUserDetailsByEmail(testEmail)
    
    console.log('')
    console.log('📋 API Response:')
    console.log(JSON.stringify(result, null, 2))
    
    console.log('')
    console.log('🎯 Key Fields:')
    console.log(`- userType: ${result.userType}`)
    console.log(`- subscriberId: ${result.subscriberId}`)
    console.log(`- permissionKeys count: ${result.permissionKeys ? result.permissionKeys.length : 0}`)
    console.log(`- permissionKeys: ${JSON.stringify(result.permissionKeys)}`)
    
    if (result.permissionKeys && result.permissionKeys.length > 0) {
      console.log('')
      console.log('✅ SUCCESS: Permission keys found!')
      console.log('Expected permissions:')
      console.log('- mrd.dashboard.view')
      console.log('- mrd.consultation.book')
      console.log('- emr.dashboard.view')
      console.log('- emr.consultation.manage, emr.consultation.view, emr.consultation.create, emr.consultation.edit')
      console.log('- emr.payment.appointment_booking')
      console.log('- emr.consultation.ambient-listening')
      console.log('- emr.lab-master.ocr')
    } else {
      console.log('')
      console.log('❌ ISSUE: No permission keys found!')
      console.log('Check the debug output above to see where the issue is.')
    }
    
  } catch (error) {
    console.error('❌ Error testing user API:', error)
  }
}

// Run the test
if (require.main === module) {
  testUserAPI()
    .then(() => {
      console.log('')
      console.log('✅ Test completed')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Test failed:', error)
      process.exit(1)
    })
}

module.exports = { testUserAPI }
