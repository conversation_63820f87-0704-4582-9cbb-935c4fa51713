/**
 * Manual script to create user-sys-role entry for existing clinic user
 * Use this if the automatic creation failed
 */

const subscriptionService = require('./src/services/subscription-service')
const subscriptionRepository = require('./src/repositories/subscription-repository')

async function createUserSysRoleForExistingUser() {
  try {
    console.log('🔧 Creating user-sys-role for existing clinic user...')
    
    // Your test user details
    const testUser = {
      email: '<EMAIL>',
      name: 'Dr. Test Clinic'
    }
    
    const organizationId = 'da42e4a1-eecb-4f51-a848-5f83ab76325a'
    const subscriberId = '130bf0b0-53ef-4291-af13-e0ba1f24d276'
    
    console.log(`User: ${testUser.email}`)
    console.log(`Organization ID: ${organizationId}`)
    console.log(`Subscriber ID: ${subscriberId}`)
    
    // Get the subscription
    console.log('📋 Fetching subscription details...')
    const subscription = await subscriptionRepository.getOrganizationSubscription(organizationId)
    
    if (!subscription) {
      console.error('❌ Subscription not found!')
      return
    }
    
    console.log(`✅ Found subscription: ${subscription.id}`)
    console.log(`Plan: ${subscription.planName}`)
    console.log(`Status: ${subscription.status}`)
    
    // Create user-sys-role entry
    console.log('🚀 Creating user-sys-role entry...')
    const result = await subscriptionService.createUserSysRoleForClinicSubscription(
      subscription,
      testUser,
      organizationId
    )
    
    if (result) {
      console.log('✅ Successfully created user-sys-role!')
      console.log(`User-sys-role ID: ${result.id}`)
      console.log(`Permission keys: ${JSON.stringify(result.permissionKeys)}`)
      console.log('')
      console.log('🎉 Now test the user API:')
      console.log('GET /api/v0.1/user?email=<EMAIL>')
      console.log('Should return permissionKeys array with permissions!')
    } else {
      console.error('❌ Failed to create user-sys-role')
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    console.error('Full error:', error)
  }
}

// Run the script
if (require.main === module) {
  createUserSysRoleForExistingUser()
    .then(() => {
      console.log('✅ Script completed')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Script failed:', error)
      process.exit(1)
    })
}

module.exports = { createUserSysRoleForExistingUser }
