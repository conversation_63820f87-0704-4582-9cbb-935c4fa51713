/**
 * Test script for the subscription-based permission system
 * This script validates the implementation of clinic subscription flow and permission handling
 */

const { v4: uuidv4 } = require('uuid')

// Test data for features
const testFeatures = [
  {
    featureName: 'MRD Dashboard',
    description: 'Dashboard for Medical Records Department',
    type: 'MRD',
    subType: 'Dashboard',
    permissionKeys: ['mrd.access', 'mrd.dashboard.view'],
  },
  {
    featureName: 'Patient Registration',
    description: 'Patient registration and management',
    type: 'MRD',
    subType: 'Registration',
    permissionKeys: [
      'mrd.patient-registration.view',
      'mrd.patient-registration.manage',
    ],
  },
  {
    featureName: 'EMR Dashboard',
    description: 'Dashboard for Electronic Medical Records',
    type: 'EMR',
    subType: 'Dashboard',
    permissionKeys: ['emr.access', 'emr.dashboard.view'],
  },
  {
    featureName: 'Consultation with AI',
    description:
      'Consultation with AI features including ambient listening and doc assist',
    type: 'EMR',
    subType: 'Consultation',
    permissionKeys: [
      'emr.consultation.view',
      'emr.consultation.create',
      'emr.consultation.edit',
      'emr.consultation.ambient-listening',
      'emr.consultation.diagnosis',
      'emr.consultation.doc-assist',
      'emr.consultation.chatbot',
    ],
  },
  {
    featureName: 'Lab Master with OCR',
    description: 'Lab master with OCR processing and AI assistance',
    type: 'EMR',
    subType: 'Lab',
    permissionKeys: [
      'emr.lab-master.view',
      'emr.lab-master.manage',
      'emr.lab-master.ocr',
      'emr.lab-master.doc-assist',
      'emr.lab-master.chatbot',
    ],
  },
  {
    featureName: 'Prescription with AI',
    description: 'Prescription management with AI assistance',
    type: 'EMR',
    subType: 'Prescription',
    permissionKeys: [
      'emr.prescription.view',
      'emr.prescription.manage',
      'emr.prescription.doc-assist',
      'emr.prescription.chatbot',
    ],
  },
  {
    featureName: 'Lifestyle Management',
    description: 'Lifestyle management with ambient listening and AI',
    type: 'EMR',
    subType: 'Lifestyle',
    permissionKeys: [
      'emr.lifestyle.manage',
      'emr.lifestyle.dashboard.view',
      'emr.lifestyle.ambient-listening',
      'emr.lifestyle.doc-assist',
      'emr.lifestyle.chatbot',
    ],
  },
  {
    featureName: 'Payment Processing',
    description: 'Payment processing for various services',
    type: 'Billing',
    subType: 'Payment',
    permissionKeys: [
      'emr.payment.appointment_booking',
      'emr.payment.lab_test',
      'emr.payment.prescription',
      'mrd.payment.patient_registration',
    ],
  },
]

// Test subscription plan
const testSubscriptionPlan = {
  planName: 'Clinic Pro Plan',
  description: 'Professional plan for clinics with full EMR and MRD features',
  validity: 'Both',
  features: {
    MRD: [
      {
        featureId: 'mrd-dashboard-feature-id',
        monthlyAmount: 500,
        yearlyAmount: 5000,
      },
      {
        featureId: 'patient-registration-feature-id',
        monthlyAmount: 300,
        yearlyAmount: 3000,
      },
    ],
    EMR: [
      {
        featureId: 'emr-dashboard-feature-id',
        monthlyAmount: 800,
        yearlyAmount: 8000,
      },
      {
        featureId: 'consultation-ai-feature-id',
        monthlyAmount: 1200,
        yearlyAmount: 12000,
      },
    ],
    Billing: [],
  },
  addOnFeatures: {
    MRD: [],
    EMR: [
      {
        featureId: 'lab-master-ocr-feature-id',
        monthlyAmount: 600,
        yearlyAmount: 6000,
      },
      {
        featureId: 'prescription-ai-feature-id',
        monthlyAmount: 400,
        yearlyAmount: 4000,
      },
      {
        featureId: 'lifestyle-management-feature-id',
        monthlyAmount: 300,
        yearlyAmount: 3000,
      },
    ],
    Billing: [
      {
        featureId: 'payment-processing-feature-id',
        monthlyAmount: 200,
        yearlyAmount: 2000,
      },
    ],
  },
  isActive: true,
}

// Test clinic subscription data
const testClinicSubscription = {
  email: '<EMAIL>',
  name: 'Dr. Test Clinic',
  phoneNumber: '+1234567890',
  planId: 'test-plan-id',
  billingType: 'yearly',
  selectedAddOnFeatures: {
    EMR: [
      {
        featureId: 'lab-master-ocr-feature-id',
        monthlyAmount: 600,
        yearlyAmount: 6000,
      },
    ],
    Billing: [
      {
        featureId: 'payment-processing-feature-id',
        monthlyAmount: 200,
        yearlyAmount: 2000,
      },
    ],
  },
  paymentId: 'test-payment-id',
}

console.log('=== Subscription-Based Permission System Test Data ===')
console.log('\n1. Test Features:')
testFeatures.forEach((feature, index) => {
  console.log(
    `   ${index + 1}. ${feature.featureName} (${feature.type}/${
      feature.subType
    })`,
  )
  console.log(`      Permissions: ${feature.permissionKeys.join(', ')}`)
})

console.log('\n2. Test Subscription Plan:')
console.log(`   Plan: ${testSubscriptionPlan.planName}`)
console.log(
  `   Base Features: ${Object.keys(testSubscriptionPlan.features)
    .map(
      (module) => `${module}(${testSubscriptionPlan.features[module].length})`,
    )
    .join(', ')}`,
)
console.log(
  `   Add-on Features: ${Object.keys(testSubscriptionPlan.addOnFeatures)
    .map(
      (module) =>
        `${module}(${testSubscriptionPlan.addOnFeatures[module].length})`,
    )
    .join(', ')}`,
)

console.log('\n3. Test Clinic Subscription:')
console.log(`   Email: ${testClinicSubscription.email}`)
console.log(`   Plan: ${testClinicSubscription.planId}`)
console.log(`   Billing: ${testClinicSubscription.billingType}`)
console.log(
  `   Selected Add-ons: ${
    Object.keys(testClinicSubscription.selectedAddOnFeatures).length
  } modules`,
)

console.log('\n=== Implementation Summary ===')
console.log('✅ Extended permission keys in common/permissions.js')
console.log(
  '✅ Added UserType constants (NORMAL, CLINIC, UNDER_SUBSCRIBED_ORGANIZATION)',
)
console.log(
  '✅ Created UserSysRole model and repository for clinic permissions',
)
console.log('✅ Extended Feature model with subType field')
console.log('✅ Created subscription features API')
console.log(
  '✅ Updated clinic subscription flow to create user-sys-role entries',
)
console.log(
  '✅ Updated user API to check user type and fetch permissions accordingly',
)
console.log('✅ Updated permission validation utils for clinic users')

console.log('\n=== API Testing Commands ===')
console.log('1. Create Features:')
console.log('   POST /api/v0.1/features')
console.log('   Body: testFeatures[0] (repeat for each feature)')

console.log('\n2. Create Subscription Plan:')
console.log('   POST /api/v0.1/subscription/plans')
console.log('   Body: testSubscriptionPlan')

console.log('\n3. Test Clinic Subscription:')
console.log('   POST /api/v0.1/clinic/subscription?action=subscribe')
console.log('   Body: testClinicSubscription')

console.log('\n4. Get Subscription Features:')
console.log(
  '   GET /api/v0.1/subscription/features?subscriberId={organizationId}',
)

console.log('\n5. Test User API:')
console.log('   GET /api/v0.1/user?email=<EMAIL>')

console.log('\n=== Expected Results ===')
console.log('✅ Clinic subscription creates user-sys-role entry')
console.log('✅ User API returns userType: "clinic" and subscriberId')
console.log('✅ Permission keys include all features from subscription')
console.log('✅ API access validation works for clinic users')
console.log('✅ Normal/organization users still use sys_role_permissions')

module.exports = {
  testFeatures,
  testSubscriptionPlan,
  testClinicSubscription,
}
