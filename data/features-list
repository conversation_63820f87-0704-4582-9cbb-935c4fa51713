{"features": [{"id": "c6538d65-49f4-4747-8f56-919b780d08dc", "featureName": "AppointmentBooking", "description": "", "type": "Billing", "subType": "", "permissionKeys": ["emr.payment.appointment_booking"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:53:27.273Z", "updated_on": "2025-11-17T11:53:27.273Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw3AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw3AAAAAAAAAA==/", "_etag": "\"3702c805-0000-0200-0000-691b0d110000\"", "_attachments": "attachments/", "_ts": 1763380497}, {"id": "9f8f1757-c3cd-4c77-a605-53fbf0c458ee", "featureName": "Billing", "description": "", "type": "Billing", "subType": "", "permissionKeys": ["payment.create"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:52:05.479Z", "updated_on": "2025-11-17T11:52:05.479Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw1AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw1AAAAAAAAAA==/", "_etag": "\"56021b00-0000-0200-0000-691bda220000\"", "_attachments": "attachments/", "_ts": 1763432994}, {"id": "51b59eb5-55dc-43b5-a181-3ff5528671a5", "featureName": "Book Consultation", "description": "Enables scheduling of doctor consultations.", "type": "MRD", "permissionKeys": ["mrd.consultation.book"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:26:28.198Z", "updated_on": "2025-11-17T07:26:28.198Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwfAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwfAAAAAAAAAA==/", "_etag": "\"5402a9fa-0000-0200-0000-691bcde40000\"", "_attachments": "attachments/", "_ts": 1763429860}, {"id": "d791b859-7d4a-4f7d-acfb-dc66006febee", "featureName": "Consultation ", "description": "", "type": "EMR", "subType": "", "permissionKeys": ["emr.consultation.manage", "emr.consultation.view", "emr.consultation.create", "emr.consultation.edit"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:27:39.010Z", "updated_on": "2025-11-17T07:27:39.010Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwjAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwjAAAAAAAAAA==/", "_etag": "\"3802f99b-0000-0200-0000-691b19ec0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "9eff5791-b160-45e1-983a-21055872c33e", "featureName": "Consultation-Ambient Listening", "description": "Automatically converts doctor–patient conversations into structured clinical notes in real time.", "type": "EMR", "subTYpe": "Consultation", "permissionKeys": ["emr.consultation.ambient-listening"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:31:17.839Z", "updated_on": "2025-11-17T07:31:17.839Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwwAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwwAAAAAAAAAA==/", "_etag": "\"5502be30-0000-0200-0000-691bd0630000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "5366f92e-3246-4de5-95df-5e6b0186442a", "featureName": "Consultation-<PERSON><PERSON> bot", "description": "Provides quick support for medical queries, suggestions, and reference material during consultation.", "type": "EMR", "subType": "Consultation", "permissionKeys": ["emr.consultation.chatbot"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:30:44.283Z", "updated_on": "2025-11-17T07:30:44.283Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwuAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwuAAAAAAAAAA==/", "_etag": "\"5502deb2-0000-0200-0000-691bd6710000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "707ed9d9-7773-4989-8e8b-d597efb16b0b", "featureName": "Consultation-Doc assist", "description": "AI-assisted tool that helps generate consultation notes, summaries, and recommendations.", "type": "EMR", "subType": "Consultation", "permissionKeys": ["emr.consultation.doc-assist"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:31:29.027Z", "updated_on": "2025-11-17T07:31:29.027Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwxAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwxAAAAAAAAAA==/", "_etag": "\"550201bf-0000-0200-0000-691bd70b0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "0a3ffbcb-0a79-4ffa-8384-f2527424778b", "featureName": "Dashboard", "description": "A quick-view summary of daily hospital activity, including patient flow, scheduled consultations, and key metrics.", "type": "MRD", "permissionKeys": ["mrd.dashboard.view"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:25:31.864Z", "updated_on": "2025-11-17T07:25:31.864Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwcAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwcAAAAAAAAAA==/", "_etag": "\"55025fcb-0000-0200-0000-691bd7a80000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "f266f098-b95e-463a-a9f9-e611f8c32655", "featureName": "Dashboard", "description": "Displays doctor-specific insights such as today's appointments, pending consultations, and recent updates.", "type": "EMR", "permissionKeys": ["emr.dashboard.view"], "subType": "", "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:31:07.423Z", "updated_on": "2025-11-17T07:31:07.423Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwvAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwvAAAAAAAAAA==/", "_etag": "\"5502b8bc-0000-0200-0000-691bd6f00000\"", "_attachments": "attachments/", "_ts": 1763432176}, {"id": "b2426a8f-c16c-4dcd-aeab-452e182edf62", "featureName": "Diagnosis", "description": "Allows doctors to add clinical findings, provisional and final diagnoses with ease.", "type": "EMR", "subType": "Consultation", "permissionKeys": ["emr.consultation.diagnosis"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:28:18.161Z", "updated_on": "2025-11-17T07:28:18.161Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwlAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwlAAAAAAAAAA==/", "_etag": "\"55028ad3-0000-0200-0000-691bd8110000\"", "_attachments": "attachments/", "_ts": 1763432465}, {"id": "7b4f1220-d80f-4f57-8dd5-7febe27efa6f", "featureName": "Lab Master-<PERSON><PERSON> bot", "description": "Helps answer lab-related questions, ranges, and clinical significance using AI.", "type": "EMR", "subType": "Lab Master", "permissionKeys": ["emr.lab-master.chatbot"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:29:50.625Z", "updated_on": "2025-11-17T07:29:50.625Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwrAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwrAAAAAAAAAA==/", "_etag": "\"550208ae-0000-0200-0000-691bd6350000\"", "_attachments": "attachments/", "_ts": 1763431989}, {"id": "040e70a7-530b-4eb7-8615-0da7ed7f98d6", "featureName": "Lab master", "description": "", "type": "EMR", "subType": "", "permissionKeys": ["emr.lab-test.view", "emr.lab-test.manage", "emr.lab-test.search"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:29:04.668Z", "updated_on": "2025-11-17T07:29:04.668Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwoAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwoAAAAAAAAAA==/", "_etag": "\"38028593-0000-0200-0000-691b19a90000\"", "_attachments": "attachments/", "_ts": 1763383721}, {"id": "a6d78eb1-6cf8-43a8-9cae-50b560929284", "featureName": "Lab master-Doc assist", "description": "Assists in interpreting lab results and generating summaries.", "type": "EMR", "subType": "Lab master", "permissionKeys": ["emr.lab-master.doc-assist"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:30:27.832Z", "updated_on": "2025-11-17T07:30:27.832Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwtAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwtAAAAAAAAAA==/", "_etag": "\"55029eaf-0000-0200-0000-691bd6490000\"", "_attachments": "attachments/", "_ts": 1763432009}, {"id": "1365f667-0b5c-4eef-b323-e738cfe56085", "featureName": "LabTest", "description": "", "type": "Billing", "subType": "", "permissionKeys": ["emr.payment.lab_test"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:54:02.654Z", "updated_on": "2025-11-17T11:54:02.654Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw4AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw4AAAAAAAAAA==/", "_etag": "\"37023b05-0000-0200-0000-691b0d0b0000\"", "_attachments": "attachments/", "_ts": 1763380491}, {"id": "2760d280-0359-4285-a21d-401af7de8b69", "featureName": "Labmaster-Department Package", "description": "", "type": "EMR", "subType": "Lab master", "permissionKeys": ["emr.test-package.view", "emr.test-package.manage"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:34:52.876Z", "updated_on": "2025-11-17T11:34:52.876Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw0AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw0AAAAAAAAAA==/", "_etag": "\"5502946f-0000-0200-0000-691bd3430000\"", "_attachments": "attachments/", "_ts": 1763431235}, {"id": "cb37cfb6-7db6-4039-9b18-c471304159b3", "featureName": "Lifestyle-Ambient listening", "description": "Captures lifestyle details from conversations and converts them into structured fields", "type": "EMR", "subType": "Lifestyle", "permissionKeys": ["emr.lifestyle.ambient-listening"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-18T02:09:57.045Z", "updated_on": "2025-11-18T02:09:57.045Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw9AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw9AAAAAAAAAA==/", "_etag": "\"5502759e-0000-0200-0000-691bd5750000\"", "_attachments": "attachments/", "_ts": 1763431797}, {"id": "95fa1f39-69ac-4223-b944-632e49da480f", "featureName": "Lifestyle-<PERSON><PERSON> bott", "description": "Provides instant support on lifestyle-related queries and standard guidelines.", "type": "EMR", "subType": "Lifestyle", "permissionKeys": ["emr.lifestyle.chatbot"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-18T02:11:47.168Z", "updated_on": "2025-11-18T02:11:47.168Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw-AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw-AAAAAAAAAA==/", "_etag": "\"55028fa7-0000-0200-0000-691bd5e30000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "10ff8743-04f5-4bff-8f0a-a521ab23e94d", "featureName": "Lifestyle-Dashboard", "description": "Presents an overview of nutrition, physical activity, and general lifestyle trends for the patient.", "type": "EMR", "subType": "Lifestyle", "permissionKeys": ["emr.lifestyle.dashboard.view"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-18T02:05:01.283Z", "updated_on": "2025-11-18T02:05:01.283Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw8AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw8AAAAAAAAAA==/", "_etag": "\"55029ee4-0000-0200-0000-691bd8db0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "d2d0b4e5-2753-41eb-9c88-0c57ffa6bd2d", "featureName": "Lifestyle-Doc Assist", "description": "Helps doctors generate lifestyle advice, nutrition plans, and physical activity recommendations.", "type": "EMR", "subType": "Lifestyle", "permissionKeys": ["emr.lifestyle.doc-assist"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-18T02:10:52.663Z", "updated_on": "2025-11-18T02:10:52.663Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw+AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw+AAAAAAAAAA==/", "_etag": "\"5502dda2-0000-0200-0000-691bd5ac0000\"", "_attachments": "attachments/", "_ts": 1763431852}, {"id": "5210d433-e9a6-4d9b-95e3-3dd3dcdb7fa9", "featureName": "OCR", "description": "Enables uploading lab reports and auto-extracts values and reference ranges using OCR.", "type": "EMR", "subType": "Lab Master", "permissionKeys": ["emr.lab-master.ocr"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:29:23.315Z", "updated_on": "2025-11-17T07:29:23.315Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwpAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwpAAAAAAAAAA==/", "_etag": "\"5502d8ac-0000-0200-0000-691bd6250000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "b0ae534c-3b51-477c-ac5a-b90e30889ea6", "featureName": "Patient Info ", "description": "Shows consolidated patient history, previous visits, and all medical records in one view.", "type": "EMR", "subType": "", "permissionKeys": ["emr.patientinfo.view", "emr.patientinfo.edit"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:27:13.733Z", "updated_on": "2025-11-17T07:27:13.733Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwiAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwiAAAAAAAAAA==/", "_etag": "\"5502f42d-0000-0200-0000-691bd0420000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "49364ffb-7885-4e44-8d38-a4092e29d674", "featureName": "Patient <PERSON>", "description": "", "type": "MRD", "subType": "Book-Consultation", "permissionKeys": ["mrd.patient-queue.manage"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:26:46.610Z", "updated_on": "2025-11-17T07:26:46.610Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwgAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwgAAAAAAAAAA==/", "_etag": "\"55025b53-0000-0200-0000-691bd1f80000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "22b04406-7dc1-405e-815d-1d758be5f3f0", "featureName": "Prescription", "description": "", "type": "EMR", "subType": "", "permissionKeys": ["emr.prescription.view", "emr.prescription.manage"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:30:08.104Z", "updated_on": "2025-11-17T07:30:08.104Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwsAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwsAAAAAAAAAA==/", "_etag": "\"38020599-0000-0200-0000-691b19d60000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "a21d4b16-d1d0-4acb-ac8c-8f93818b5f91", "featureName": "Prescription-Chat bot", "description": "Supports quick medical queries related to medicines, interactions, and usage.", "type": "EMR", "subType": "Lifestyle", "permissionKeys": ["emr.prescription.chatbot"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-18T02:27:05.581Z", "updated_on": "2025-11-18T02:27:05.581Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfxAAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfxAAAAAAAAAAA==/", "_etag": "\"5502a3f1-0000-0200-0000-691bd9790000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "12c9dbf4-6a92-489d-8878-4204e14663e6", "featureName": "Prescription-Department Package", "description": "", "type": "EMR", "subType": "Prescription", "permissionKeys": ["emr.medicine-package.view", "emr.medicine-package.manage"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:34:07.792Z", "updated_on": "2025-11-17T11:34:07.792Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwzAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwzAAAAAAAAAA==/", "_etag": "\"5502a670-0000-0200-0000-691bd34f0000\"", "_attachments": "attachments/", "_ts": 1763431247}, {"id": "3efe5655-917b-47d8-b7a3-3a1a29255105", "featureName": "Prescription-Doc Assist", "description": "Suggests medicines, dosages, and treatment plans using AI to streamline prescription creation.", "type": "EMR", "subType": "Prescription", "permissionKeys": ["emr.prescription.doc-assist"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:28:38.478Z", "updated_on": "2025-11-17T07:28:38.478Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfwmAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfwmAAAAAAAAAA==/", "_etag": "\"5502e8d9-0000-0200-0000-691bd85c0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "fcea793e-6b86-4cb2-aa7f-1ae5188f58d0", "featureName": "<PERSON><PERSON>", "description": "Allows staff to record, update, patient vital parameters before consultation", "type": "MRD", "permissionKeys": ["mrd.manage-patient.edit", "mrd.manage-patient.view", "mrd.vitals.view", "mrd.vitals.manage"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T07:25:58.484Z", "updated_on": "2025-11-17T07:25:58.484Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfweAAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfweAAAAAAAAAA==/", "_etag": "\"5602ef19-0000-0200-0000-691bdb6c0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "38a5d170-72a5-4285-9c83-2f0dfc345d0b", "featureName": "patientRegistration", "description": "Captures and manages new patient details, storing demographic and essential medical information", "type": "MRD", "subType": "", "permissionKeys": ["mrd.payment.patient_registration"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:52:34.300Z", "updated_on": "2025-11-17T11:52:34.300Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw2AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw2AAAAAAAAAA==/", "_etag": "\"540218ff-0000-0200-0000-691bce170000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "38a5d170-72a5-4285-9c83-2f0dfc345d0m", "featureName": "patientRegistration", "description": "", "type": "Billing", "subType": "", "permissionKeys": ["mrd.payment.patient_registration"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:52:34.300Z", "updated_on": "2025-11-17T11:52:34.300Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw7AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw7AAAAAAAAAA==/", "_etag": "\"5502a708-0000-0200-0000-691bce890000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "0f585027-80cc-40ec-b4da-b88f7a318a44", "featureName": "prescription", "description": "", "type": "Billing", "subType": "", "permissionKeys": ["emr.payment.prescription"], "isActive": true, "created_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "created_on": "2025-11-17T11:54:35.444Z", "updated_on": "2025-11-17T11:54:35.444Z", "updated_by": "80fa2c02-96ae-4524-ab82-1f3832936928", "_rid": "gG1xAKn1qfw5AAAAAAAAAA==", "_self": "dbs/gG1xAA==/colls/gG1xAKn1qfw=/docs/gG1xAKn1qfw5AAAAAAAAAA==/", "_etag": "\"37027303-0000-0200-0000-691b0cfb0000\"", "_attachments": "attachments/", "_ts": 1763380475}], "count": 30}